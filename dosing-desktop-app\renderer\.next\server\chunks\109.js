"use strict";exports.id=109,exports.ids=[109],exports.modules={36246:(n,e,i)=>{i.d(e,{Z:()=>c});var t=i(25773),a=i(16689);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M924.8 385.6a446.7 446.7 0 00-96-142.4 446.7 446.7 0 00-142.4-96C631.1 123.8 572.5 112 512 112s-119.1 11.8-174.4 35.2a446.7 446.7 0 00-142.4 96 446.7 446.7 0 00-96 142.4C75.8 440.9 64 499.5 64 560c0 132.7 58.3 257.7 159.9 343.1l1.7 1.4c5.8 4.8 13.1 7.5 20.6 7.5h531.7c7.5 0 14.8-2.7 20.6-7.5l1.7-1.4C901.7 817.7 960 692.7 960 560c0-60.5-11.9-119.1-35.2-174.4zM761.4 836H262.6A371.12 371.12 0 01140 560c0-99.4 38.7-192.8 109-263 70.3-70.3 163.7-109 263-109 99.4 0 192.8 38.7 263 109 70.3 70.3 109 163.7 109 263 0 105.6-44.5 205.5-122.6 276zM623.5 421.5a8.03 8.03 0 00-11.3 0L527.7 506c-18.7-5-39.4-.2-54.1 14.5a55.95 55.95 0 000 79.2 55.95 55.95 0 0079.2 0 55.87 55.87 0 0014.5-54.1l84.5-84.5c3.1-3.1 3.1-8.2 0-11.3l-28.3-28.3zM490 320h44c4.4 0 8-3.6 8-8v-80c0-4.4-3.6-8-8-8h-44c-4.4 0-8 3.6-8 8v80c0 4.4 3.6 8 8 8zm260 218v44c0 4.4 3.6 8 8 8h80c4.4 0 8-3.6 8-8v-44c0-4.4-3.6-8-8-8h-80c-4.4 0-8 3.6-8 8zm12.7-197.2l-31.1-31.1a8.03 8.03 0 00-11.3 0l-56.6 56.6a8.03 8.03 0 000 11.3l31.1 31.1c3.1 3.1 8.2 3.1 11.3 0l56.6-56.6c3.1-3.1 3.1-8.2 0-11.3zm-458.6-31.1a8.03 8.03 0 00-11.3 0l-31.1 31.1a8.03 8.03 0 000 11.3l56.6 56.6c3.1 3.1 8.2 3.1 11.3 0l31.1-31.1c3.1-3.1 3.1-8.2 0-11.3l-56.6-56.6zM262 530h-80c-4.4 0-8 3.6-8 8v44c0 4.4 3.6 8 8 8h80c4.4 0 8-3.6 8-8v-44c0-4.4-3.6-8-8-8z"}}]},name:"dashboard",theme:"outlined"};var r=i(79194);let c=a.forwardRef(function(n,e){return a.createElement(r.Z,(0,t.Z)({},n,{ref:e,icon:l}))})},46463:(n,e,i)=>{i.d(e,{Z:()=>q});var t=i(16689),a=i(43657),l=i(59003),r=i.n(l),c=i(8662),o=i.n(c),d=i(82396),s=i(28317),u=i(76393),h=i(2629),g=i(36837),m=i(52727),$=i(79712),b=i(92929),p=i(83505),I=i(10045);let v=n=>{let{componentCls:e,trackHeightSM:i,trackPadding:t,trackMinWidthSM:a,innerMinMarginSM:l,innerMaxMarginSM:r,handleSizeSM:c,calc:o}=n,d=`${e}-inner`,s=(0,m.unit)(o(c).add(o(t).mul(2)).equal()),u=(0,m.unit)(o(r).mul(2).equal());return{[e]:{[`&${e}-small`]:{minWidth:a,height:i,lineHeight:(0,m.unit)(i),[`${e}-inner`]:{paddingInlineStart:r,paddingInlineEnd:l,[`${d}-checked, ${d}-unchecked`]:{minHeight:i},[`${d}-checked`]:{marginInlineStart:`calc(-100% + ${s} - ${u})`,marginInlineEnd:`calc(100% - ${s} + ${u})`},[`${d}-unchecked`]:{marginTop:o(i).mul(-1).equal(),marginInlineStart:0,marginInlineEnd:0}},[`${e}-handle`]:{width:c,height:c},[`${e}-loading-icon`]:{top:o(o(c).sub(n.switchLoadingIconSize)).div(2).equal(),fontSize:n.switchLoadingIconSize},[`&${e}-checked`]:{[`${e}-inner`]:{paddingInlineStart:l,paddingInlineEnd:r,[`${d}-checked`]:{marginInlineStart:0,marginInlineEnd:0},[`${d}-unchecked`]:{marginInlineStart:`calc(100% - ${s} + ${u})`,marginInlineEnd:`calc(-100% + ${s} - ${u})`}},[`${e}-handle`]:{insetInlineStart:`calc(100% - ${(0,m.unit)(o(c).add(t).equal())})`}},[`&:not(${e}-disabled):active`]:{[`&:not(${e}-checked) ${d}`]:{[`${d}-unchecked`]:{marginInlineStart:o(n.marginXXS).div(2).equal(),marginInlineEnd:o(n.marginXXS).mul(-1).div(2).equal()}},[`&${e}-checked ${d}`]:{[`${d}-checked`]:{marginInlineStart:o(n.marginXXS).mul(-1).div(2).equal(),marginInlineEnd:o(n.marginXXS).div(2).equal()}}}}}}},S=n=>{let{componentCls:e,handleSize:i,calc:t}=n;return{[e]:{[`${e}-loading-icon${n.iconCls}`]:{position:"relative",top:t(t(i).sub(n.fontSize)).div(2).equal(),color:n.switchLoadingIconColor,verticalAlign:"top"},[`&${e}-checked ${e}-loading-icon`]:{color:n.switchColor}}}},f=n=>{let{componentCls:e,trackPadding:i,handleBg:t,handleShadow:a,handleSize:l,calc:r}=n,c=`${e}-handle`;return{[e]:{[c]:{position:"absolute",top:i,insetInlineStart:i,width:l,height:l,transition:`all ${n.switchDuration} ease-in-out`,"&::before":{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,backgroundColor:t,borderRadius:r(l).div(2).equal(),boxShadow:a,transition:`all ${n.switchDuration} ease-in-out`,content:'""'}},[`&${e}-checked ${c}`]:{insetInlineStart:`calc(100% - ${(0,m.unit)(r(l).add(i).equal())})`},[`&:not(${e}-disabled):active`]:{[`${c}::before`]:{insetInlineEnd:n.switchHandleActiveInset,insetInlineStart:0},[`&${e}-checked ${c}::before`]:{insetInlineEnd:0,insetInlineStart:n.switchHandleActiveInset}}}}},w=n=>{let{componentCls:e,trackHeight:i,trackPadding:t,innerMinMargin:a,innerMaxMargin:l,handleSize:r,calc:c}=n,o=`${e}-inner`,d=(0,m.unit)(c(r).add(c(t).mul(2)).equal()),s=(0,m.unit)(c(l).mul(2).equal());return{[e]:{[o]:{display:"block",overflow:"hidden",borderRadius:100,height:"100%",paddingInlineStart:l,paddingInlineEnd:a,transition:`padding-inline-start ${n.switchDuration} ease-in-out, padding-inline-end ${n.switchDuration} ease-in-out`,[`${o}-checked, ${o}-unchecked`]:{display:"block",color:n.colorTextLightSolid,fontSize:n.fontSizeSM,transition:`margin-inline-start ${n.switchDuration} ease-in-out, margin-inline-end ${n.switchDuration} ease-in-out`,pointerEvents:"none",minHeight:i},[`${o}-checked`]:{marginInlineStart:`calc(-100% + ${d} - ${s})`,marginInlineEnd:`calc(100% - ${d} + ${s})`},[`${o}-unchecked`]:{marginTop:c(i).mul(-1).equal(),marginInlineStart:0,marginInlineEnd:0}},[`&${e}-checked ${o}`]:{paddingInlineStart:a,paddingInlineEnd:l,[`${o}-checked`]:{marginInlineStart:0,marginInlineEnd:0},[`${o}-unchecked`]:{marginInlineStart:`calc(100% - ${d} + ${s})`,marginInlineEnd:`calc(-100% + ${d} - ${s})`}},[`&:not(${e}-disabled):active`]:{[`&:not(${e}-checked) ${o}`]:{[`${o}-unchecked`]:{marginInlineStart:c(t).mul(2).equal(),marginInlineEnd:c(t).mul(-1).mul(2).equal()}},[`&${e}-checked ${o}`]:{[`${o}-checked`]:{marginInlineStart:c(t).mul(-1).mul(2).equal(),marginInlineEnd:c(t).mul(2).equal()}}}}}},k=n=>{let{componentCls:e,trackHeight:i,trackMinWidth:t}=n;return{[e]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,b.Wf)(n)),{position:"relative",display:"inline-block",boxSizing:"border-box",minWidth:t,height:i,lineHeight:(0,m.unit)(i),verticalAlign:"middle",background:n.colorTextQuaternary,border:"0",borderRadius:100,cursor:"pointer",transition:`all ${n.motionDurationMid}`,userSelect:"none",[`&:hover:not(${e}-disabled)`]:{background:n.colorTextTertiary}}),(0,b.Qy)(n)),{[`&${e}-checked`]:{background:n.switchColor,[`&:hover:not(${e}-disabled)`]:{background:n.colorPrimaryHover}},[`&${e}-loading, &${e}-disabled`]:{cursor:"not-allowed",opacity:n.switchDisabledOpacity,"*":{boxShadow:"none",cursor:"not-allowed"}},[`&${e}-rtl`]:{direction:"rtl"}})}},y=(0,p.I$)("Switch",n=>{let e=(0,I.mergeToken)(n,{switchDuration:n.motionDurationMid,switchColor:n.colorPrimary,switchDisabledOpacity:n.opacityLoading,switchLoadingIconSize:n.calc(n.fontSizeIcon).mul(.75).equal(),switchLoadingIconColor:`rgba(0, 0, 0, ${n.opacityLoading})`,switchHandleActiveInset:"-30%"});return[k(e),w(e),f(e),S(e),v(e)]},n=>{let{fontSize:e,lineHeight:i,controlHeight:t,colorWhite:a}=n,l=e*i,r=t/2,c=l-4,o=r-4;return{trackHeight:l,trackHeightSM:r,trackMinWidth:2*c+8,trackMinWidthSM:2*o+4,trackPadding:2,handleBg:a,handleSize:c,handleSizeSM:o,handleShadow:`0 2px 4px 0 ${new $.FastColor("#00230b").setA(.2).toRgbString()}`,innerMinMargin:c/2,innerMaxMargin:c+2+4,innerMinMarginSM:o/2,innerMaxMarginSM:o+2+4}});var E=function(n,e){var i={};for(var t in n)Object.prototype.hasOwnProperty.call(n,t)&&0>e.indexOf(t)&&(i[t]=n[t]);if(null!=n&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,t=Object.getOwnPropertySymbols(n);a<t.length;a++)0>e.indexOf(t[a])&&Object.prototype.propertyIsEnumerable.call(n,t[a])&&(i[t[a]]=n[t[a]]);return i};let M=t.forwardRef((n,e)=>{let{prefixCls:i,size:l,disabled:c,loading:m,className:$,rootClassName:b,style:p,checked:I,value:v,defaultChecked:S,defaultValue:f,onChange:w}=n,k=E(n,["prefixCls","size","disabled","loading","className","rootClassName","style","checked","value","defaultChecked","defaultValue","onChange"]),[M,q]=(0,d.Z)(!1,{value:null!=I?I:v,defaultValue:null!=S?S:f}),{getPrefixCls:x,direction:C,switch:z}=t.useContext(u.E_),O=t.useContext(h.Z),j=(null!=c?c:O)||m,H=x("switch",i),A=t.createElement("div",{className:`${H}-handle`},m&&t.createElement(a.Z,{className:`${H}-loading-icon`})),[D,T,L]=y(H),Z=(0,g.Z)(l),X=r()(null==z?void 0:z.className,{[`${H}-small`]:"small"===Z,[`${H}-loading`]:m,[`${H}-rtl`]:"rtl"===C},$,b,T,L),N=Object.assign(Object.assign({},null==z?void 0:z.style),p);return D(t.createElement(s.Z,{component:"Switch"},t.createElement(o(),Object.assign({},k,{checked:M,onChange:function(){q(arguments.length<=0?void 0:arguments[0]),null==w||w.apply(void 0,arguments)},prefixCls:H,className:X,style:N,disabled:j,ref:e,loadingIcon:A}))))});M.__ANT_SWITCH=!0;let q=M},17666:(n,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=function(n){return+setTimeout(n,16)},t=function(n){return clearTimeout(n)};"undefined"!=typeof window&&"requestAnimationFrame"in window&&(i=function(n){return window.requestAnimationFrame(n)},t=function(n){return window.cancelAnimationFrame(n)});var a=0,l=new Map,r=function(n){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,t=a+=1;return function e(a){if(0===a)l.delete(t),n();else{var r=i(function(){e(a-1)});l.set(t,r)}}(e),t};r.cancel=function(n){var e=l.get(n);return l.delete(n),t(e)},e.default=r}};