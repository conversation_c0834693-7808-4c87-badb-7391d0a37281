(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[18],{999:function(e,i,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/vietplants/calibsensors",function(){return n(7288)}])},7288:function(e,i,n){"use strict";n.r(i),n.d(i,{default:function(){return q}});var t=n(4246),l=n(9739),o=n(7378),r=n(9341),s=n(22),c=n(5475),d=n(6316),a=n(9774),h=n(1216),u=n(8198),g=n(6502),f=n(4580),x=n(4776),m=e=>{var i;let{functionItem:n,handleCancelCalibration:l,setIsCalibProgressFinished:r}=e,[s,c]=(0,o.useState)(!1);return(0,o.useEffect)(()=>{r(!1)},[]),(0,t.jsx)("div",{style:{zIndex:999,height:"100vh",width:"100vw",position:"fixed",top:0,left:0,backgroundColor:"rgba(0,0,0,0.1)",backdropFilter:"blur(1px)",display:"flex",justifyContent:"center",alignItems:"center",padding:"20px",boxSizing:"border-box"},children:(0,t.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:12,background:"#fff",borderRadius:16,padding:20,minWidth:300,maxWidth:"90vw",width:"fit-content",minHeight:180,maxHeight:"90vh",justifyContent:"center",alignItems:"center",boxShadow:"0 4px 20px rgba(0, 0, 0, 0.15)",overflow:"auto",boxSizing:"border-box"},children:[(0,t.jsxs)("p",{style:{fontSize:16,textAlign:"center",margin:"0 0 16px 0",wordWrap:"break-word",lineHeight:1.4},children:["Đang hiệu chuẩn cho Bơm"," ",n.identifier?(null===(i=n.identifier.match(/\d+$/))||void 0===i?void 0:i[0])||n.identifier:""]}),(0,t.jsxs)("div",{style:{display:"flex",gap:12,flexDirection:"column",width:"100%",maxWidth:280},children:[(0,t.jsx)(d.ZP,{type:"primary",onClick:()=>{l(),r(!0)},style:{backgroundColor:"#52c41a",borderColor:"#52c41a",width:"100%",height:"auto",padding:"8px 16px",whiteSpace:"normal",wordWrap:"break-word"},children:"✅ Ho\xe0n th\xe0nh v\xe0 nhập kết quả"}),(0,t.jsx)(d.ZP,{danger:!0,type:"primary",onClick:()=>c(!0),icon:(0,t.jsx)(x.Z,{}),style:{width:"100%",height:"auto",padding:"8px 16px",whiteSpace:"normal",wordWrap:"break-word"},children:"❌ Hủy bỏ hiệu chuẩn"})]}),(0,t.jsx)(f.Z,{title:"Hủy bỏ hiệu chuẩn",content:(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("p",{children:"Điều n\xe0y sẽ hủy bỏ qu\xe1 tr\xecnh hiệu chuẩn của bơm"}),(0,t.jsx)("p",{children:"Bạn c\xf3 chắc chắn muốn l\xe0m điều đ\xf3 kh\xf4ng?"}),(0,t.jsx)(d.ZP,{danger:!0,type:"primary",onClick:()=>l(),icon:(0,t.jsx)(x.Z,{}),children:"OK"})]}),trigger:"click",open:s,onOpenChange:e=>c(e)})]})})},p=n(1421),b=n(7693),v=n.n(b),y=n(1624),j=n(9960),C=n(4554),T=n(2380),_=n(2146),D=n(2092),L=n(1468);let{Text:k}=C.default;var w=e=>{let{pumpIndex:i,oldCalibValue:n,latestHoldingValue:l,actualMlValue:o,isCalibrationInProgress:r,isCalibProgressFinished:s}=e,c=(()=>{if(!n||!l||!o)return null;let e=l/n,i=o/e;return{runTime:e.toFixed(4),newCalibValue:i.toFixed(4),roundedNewCalib:Math.round(100*i)/100}})();return(0,t.jsxs)(T.Z,{size:"small",title:"Debug Info - Bơm ".concat(i+1),style:{marginTop:16},children:[(0,t.jsx)(_.Z,{message:"Th\xf4ng tin debug",description:"Th\xf4ng tin n\xe0y gi\xfap kiểm tra qu\xe1 tr\xecnh t\xednh to\xe1n hiệu chuẩn",type:"info",showIcon:!0,style:{marginBottom:16}}),(0,t.jsxs)(D.Z,{size:"small",column:1,bordered:!0,children:[(0,t.jsx)(D.Z.Item,{label:"Pump Index",children:(0,t.jsx)(L.Z,{color:"blue",children:i+1})}),(0,t.jsx)(D.Z.Item,{label:"Keys sẽ được gửi",children:(0,t.jsxs)("div",{children:[(0,t.jsxs)(k,{code:!0,children:["CALIB_ACTUAL_ML_BOM_",i+1]}),(0,t.jsx)("br",{}),(0,t.jsxs)(k,{code:!0,children:["CALCULATE_CALIB_BOM_",i+1]})]})}),(0,t.jsxs)(D.Z.Item,{label:"Hệ số hiệu chuẩn cũ",children:[(0,t.jsx)(k,{strong:!0,children:null!==n?n:"Chưa c\xf3 dữ liệu"}),(0,t.jsx)("br",{}),(0,t.jsxs)(k,{type:"secondary",style:{fontSize:11},children:["HOLDING_CALIB_BOM_",i+1]})]}),(0,t.jsxs)(D.Z.Item,{label:"Gi\xe1 trị SET ML hiện tại",children:[(0,t.jsx)(k,{strong:!0,children:null!==l?"".concat(l," ml"):"Chưa c\xf3 dữ liệu"}),(0,t.jsx)("br",{}),(0,t.jsxs)(k,{type:"secondary",style:{fontSize:11},children:["HOLDING_SETML_BOM_",i+1]})]}),(0,t.jsx)(D.Z.Item,{label:"Gi\xe1 trị thực tế đo được",children:(0,t.jsx)(k,{strong:!0,style:{color:o?"#52c41a":"#ff4d4f"},children:null!==o?"".concat(o," ml"):"Chưa nhập"})}),(0,t.jsx)(D.Z.Item,{label:"Trạng th\xe1i",children:(0,t.jsxs)("div",{children:[(0,t.jsx)(L.Z,{color:s?"green":"default",children:s?"Đ\xe3 ho\xe0n th\xe0nh chạy":"Chưa chạy"}),(0,t.jsx)(L.Z,{color:r?"processing":"default",children:r?"Đang t\xednh to\xe1n":"Chờ t\xednh to\xe1n"})]})}),c&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(D.Z.Item,{label:"Thời gian chạy dự kiến",children:(0,t.jsxs)(k,{code:!0,children:[c.runTime," gi\xe2y"]})}),(0,t.jsx)(D.Z.Item,{label:"Hệ số mới dự kiến",children:(0,t.jsxs)("div",{children:[(0,t.jsx)(k,{code:!0,children:c.newCalibValue}),(0,t.jsx)("br",{}),(0,t.jsxs)(k,{strong:!0,children:["L\xe0m tr\xf2n: ",c.roundedNewCalib]})]})}),(0,t.jsx)(D.Z.Item,{label:"C\xf4ng thức",children:(0,t.jsxs)("div",{children:[(0,t.jsxs)(k,{code:!0,children:["RUN_TIME = ",l," / ",n," ="," ",c.runTime]}),(0,t.jsx)("br",{}),(0,t.jsxs)(k,{code:!0,children:["NEW_CALIB = ",o," / ",c.runTime," ="," ",c.newCalibValue]})]})})]})]}),!c&&(0,t.jsx)(_.Z,{message:"Chưa đủ dữ liệu để t\xednh to\xe1n",description:"Cần c\xf3 đầy đủ: hệ số cũ, gi\xe1 trị SET ML, v\xe0 gi\xe1 trị thực tế đo được",type:"warning",showIcon:!0,style:{marginTop:16}})]})};let I={isDevelopment:!1,isDebugEnabled:!1,features:{calibrationDebugInfo:!1,calibrationTestTool:!1,apiLogger:!1,consoleLogging:!0}},S=()=>I.isDevelopment||I.isDebugEnabled,Z=e=>S()&&I.features[e];window.calibrationDebug={toggle:()=>{I.isDebugEnabled=!I.isDebugEnabled,console.log("Debug mode: ".concat(I.isDebugEnabled?"ON":"OFF"))},toggleFeature:e=>{I.features[e]=!I.features[e],console.log("Debug feature ".concat(e,": ").concat(I.features[e]?"ON":"OFF"))},config:I,log:(e,i)=>{Z("consoleLogging")&&console.log("[CALIBRATION_DEBUG] ".concat(e),i||"")}};var B=e=>{var i,n,f,x,b,C,T,_,D,L,k,I;let{key:S,indexOfPump:B,functionItem:O,functionsForControl:A,functionForOldCalibration:E,isThereAnyCalibration:M,setIsThereAnyCalibration:N}=e;console.log("key ne: ",B);let{calibrationInformation:P,setCalibrationInformation:H,deviceId:z}=(0,l.Z)(),[W,F]=(0,o.useState)(!1),[U,R]=(0,o.useState)(!1),[q,G]=(0,o.useState)(null);(0,o.useEffect)(()=>{if(P&&0!==P.length&&"fulfill"===q){let e=[...P];e[B]={...e[B],fulfill:W},H(e)}},[W,q]);let[Q,V]=(0,o.useState)(!1),[K,$]=(0,o.useState)(!1);(0,o.useEffect)(()=>{if(P&&0!==P.length&&"calibration"===q){let e=[...P];e[B]={...e[B],calibration:Q,startTimestampCalibration:Q?v()():null,totalTimeCalibration:Y>0?eh/Y:0},H(e)}},[Q,q]);let{run:X}=(0,p.Z)(),J=async()=>{let e=[...P];e[B]={...e[B],calibration:!1},await X({device_id_thingsboard:z,method:"set_state",params:{[A.onOffCoil.identifier]:!1}}),H(e),N(null),G(null)},[Y,ee]=(0,o.useState)(null),[ei,en]=(0,o.useState)(null),[et,el]=(0,o.useState)(!1),[eo,er]=(0,o.useState)(0),es=async()=>{if(!ei)return;let e="CALIB_ACTUAL_ML_BOM_".concat(B+1);console.log({device_id_thingsboard:z,method:"set_state",params:{[e]:ei}}),await X({device_id_thingsboard:z,method:"set_state",params:{[e]:ei}})},ec=async()=>{let e="CALCULATE_CALIB_BOM_".concat(B+1);console.log({device_id_thingsboard:z,method:"set_state",params:{[e]:!0}});try{await X({device_id_thingsboard:z,method:"set_state",params:{[e]:!0}}),el(!0),c.ZP.success("Đ\xe3 gửi y\xeau cầu t\xednh to\xe1n hiệu chuẩn"),setTimeout(()=>{el(!1)},3e4)}catch(e){c.ZP.error("Lỗi khi gửi y\xeau cầu hiệu chuẩn"),console.error("Calibration trigger error:",e)}},{subscribe:ed,unsubscribe:ea}=(0,y.T)();(0,o.useEffect)(()=>{if(!z||!et)return;let e="CALCULATE_CALIB_BOM_".concat(B+1),i=ed([(0,j.v)(z)],i=>{try{let n=JSON.parse(i);if(Array.isArray(n)){let i=n.find(i=>i.key===e);i&&!1===i.value&&(el(!1),c.ZP.success("Hiệu chuẩn ho\xe0n tất! Hệ số mới đ\xe3 được cập nhật."))}}catch(e){console.error("MQTT message parsing error:",e)}});return()=>{i.length>0&&ea(i)}},[z,et,B,ed,ea]),(0,o.useEffect)(()=>{if(!(null==E?void 0:E.identifier)||!z)return;(0,r.$U)({deviceId:z,keys:[E.identifier]}).then(e=>{var i;if(!e)return;let n=(null==e?void 0:null===(i=e.data)||void 0===i?void 0:i[E.identifier])||[];if(console.log("latestDataOfOldCalibration",n),n.length>0){let e=n[n.length-1];if(e){let i=Number(null==e?void 0:e.value);ef(i),ee(i)}}});let e=ed([(0,j.v)(z)],e=>{try{let i=JSON.parse(e);if(Array.isArray(i)){let e=i.find(e=>e.key===E.identifier);if(e){let i=Number(e.value);ef(i),ee(i)}}}catch(e){console.error("MQTT message parsing error for calib coeff:",e)}});return()=>{e.length>0&&ea(e)}},[z,null==E?void 0:E.identifier,ed,ea]),(0,o.useEffect)(()=>{var e;0!==eo&&((null==E?void 0:E.identifier)&&z&&(0,r.$U)({deviceId:z,keys:[E.identifier]}).then(e=>{var i;if(!e)return;let n=(null==e?void 0:null===(i=e.data)||void 0===i?void 0:i[E.identifier])||[];if(n.length>0){let e=n[n.length-1];if(e){let i=Number(null==e?void 0:e.value);ef(i),ee(i)}}}),(null==A?void 0:null===(e=A.digitCoil)||void 0===e?void 0:e.identifier)&&z&&(0,r.$U)({deviceId:z,keys:[A.digitCoil.identifier]}).then(e=>{var i;if(!e)return;let n=(null==e?void 0:null===(i=e.data)||void 0===i?void 0:i[A.digitCoil.identifier])||[];if(n.length>0){let e=n[n.length-1];e&&eu(Number(null==e?void 0:e.value))}}))},[eo,z,null==E?void 0:E.identifier,null==A?void 0:null===(i=A.digitCoil)||void 0===i?void 0:i.identifier]),console.log("functionForHolding: ",null==A?void 0:A.digitCoil),console.log("functionForOldCalibration: ",E);let[eh,eu]=(0,o.useState)(null),[eg,ef]=(0,o.useState)(null),[ex,em]=(0,o.useState)(!1);return P&&0!==P.length?(0,t.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:8,borderRadius:8,padding:8,width:"calc(50% - 16px)",backgroundColor:"#fff",height:"100%"},children:[(0,t.jsx)("p",{style:{marginBottom:16,fontWeight:"bold",color:"rgb(40,40,40)"},children:O.label}),(0,t.jsxs)("div",{style:{width:"100%",display:"flex",flexDirection:"column",alignItems:"center",gap:8},children:[(0,t.jsxs)("div",{style:{width:"100%",display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"space-between",height:"100%",gap:8},children:[(0,t.jsx)(d.ZP,{style:{width:"50%"},onClick:()=>{R(!0),G("fulfill")},children:(0,t.jsx)("p",{style:{fontSize:12,margin:0,color:(null==P?void 0:null===(n=P[B])||void 0===n?void 0:n.fulfill)?"rgb(153, 255, 0)":"rgb(100,100,100)",fontWeight:"bold"},children:"Bơm đầy ống"})}),(0,t.jsx)(d.ZP,{style:{width:"50%"},onClick:()=>{$(!0),G("calibration")},children:(0,t.jsx)("p",{style:{fontSize:12,margin:0,color:(null==P?void 0:null===(f=P[B])||void 0===f?void 0:f.calibration)?"rgb(153, 255, 0)":"rgb(100,100,100)",fontWeight:"bold"},children:"Tiến h\xe0nh hiệu chuẩn"})})]}),E&&(0,t.jsxs)("div",{style:{width:"100%",display:"flex",flexDirection:"column",gap:4,marginTop:8,padding:8,backgroundColor:"#f8f9fa",borderRadius:4,border:"1px solid #e9ecef"},children:[(0,t.jsxs)("p",{style:{margin:0,fontSize:12,fontWeight:"bold",color:"#495057"},children:[E.label,":"]}),(0,t.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:8},children:[(0,t.jsx)(s.Z,{value:eg,readOnly:!0,disabled:!0,style:{width:"100%",backgroundColor:"#ffffff",cursor:"default"},placeholder:"Đang tải..."}),(0,t.jsx)("span",{style:{fontSize:12,color:"#6c757d",fontWeight:"500"},children:"ml/s"})]})]}),(0,t.jsx)("p",{style:{fontSize:13,color:"#45c3a1",fontWeight:"bold",margin:0},children:(null==P?void 0:null===(x=P[B])||void 0===x?void 0:x.isPumpActivedBefore)?(0,t.jsxs)("p",{style:{color:"red"},children:["`$",A.onOffCoil.label," đ\xe3 hoạt động trước đ\xf3 !`"]}):(null==P?void 0:null===(b=P[B])||void 0===b?void 0:b.fulfill)?"".concat(A.onOffCoil.label," đang bơm đầy ống..."):(null==P?void 0:null===(C=P[B])||void 0===C?void 0:C.calibration)?"".concat(A.onOffCoil.label," đang hiệu chuẩn..."):""}),(null==P?void 0:null===(T=P[B])||void 0===T?void 0:T.calibration)&&(0,t.jsx)(m,{functionItem:O,handleCancelCalibration:J,setIsCalibProgressFinished:em}),(0,t.jsxs)("div",{style:{display:"flex",flexDirection:"column",width:"100%",gap:16},children:[((null==P?void 0:null===(_=P[B])||void 0===_?void 0:_.calibration)||ex)&&!et&&(0,t.jsxs)("div",{style:{marginTop:16,width:"100%",display:"flex",flexDirection:"column",gap:8},children:[(0,t.jsx)("div",{style:{padding:8,backgroundColor:(null==P?void 0:null===(D=P[B])||void 0===D?void 0:D.calibration)?"#fff7e6":"#f6ffed",borderRadius:4,border:"1px solid ".concat((null==P?void 0:null===(L=P[B])||void 0===L?void 0:L.calibration)?"#ffd591":"#b7eb8f")},children:(0,t.jsx)("p",{style:{margin:0,fontSize:12,color:(null==P?void 0:null===(k=P[B])||void 0===k?void 0:k.calibration)?"#d46b08":"#52c41a",fontWeight:"bold"},children:(null==P?void 0:null===(I=P[B])||void 0===I?void 0:I.calibration)?"\uD83D\uDCA1 Bạn c\xf3 thể nhập kết quả đo ngay b\xe2y giờ hoặc chờ hết thời gian":"✅ Đ\xe3 ho\xe0n th\xe0nh chạy bơm, h\xe3y nhập kết quả đo được"})}),(0,t.jsx)("p",{style:{margin:0,fontSize:14,fontWeight:"bold"},children:"Bước 1: Nhập lượng thực tế đo được (ml)"}),(0,t.jsxs)("div",{style:{display:"flex",flexDirection:"row",alignItems:"center",gap:8},children:[(0,t.jsx)(s.Z,{placeholder:"Nhập số liệu thực tế (ml)",style:{width:"100%"},onChange:e=>en(e),value:ei}),(0,t.jsx)(d.ZP,{type:"primary",onClick:es,disabled:!ei,children:"Lưu"})]})]}),ex&&ei&&!et&&(0,t.jsxs)("div",{style:{width:"100%",display:"flex",flexDirection:"column",gap:8},children:[(0,t.jsx)("p",{style:{margin:0,fontSize:14,fontWeight:"bold"},children:"Bước 2: T\xednh to\xe1n hệ số hiệu chuẩn mới"}),(0,t.jsx)(d.ZP,{type:"primary",onClick:ec,style:{backgroundColor:"#52c41a"},children:"T\xednh to\xe1n v\xe0 cập nhật hệ số hiệu chuẩn"})]}),et&&(0,t.jsxs)("div",{style:{width:"100%",display:"flex",flexDirection:"column",alignItems:"center",gap:8,padding:16,backgroundColor:"#f6ffed",border:"1px solid #b7eb8f",borderRadius:8},children:[(0,t.jsx)("p",{style:{margin:0,fontSize:14,fontWeight:"bold",color:"#52c41a"},children:"\uD83D\uDD04 Đang t\xednh to\xe1n hệ số hiệu chuẩn..."}),(0,t.jsx)("p",{style:{margin:0,fontSize:12,color:"#666"},children:"Đang xử l\xfd v\xe0 cập nhật gi\xe1 trị mới"})]})]}),Z("calibrationDebugInfo")&&(0,t.jsx)(a.Z,{size:"small",style:{marginTop:16},items:[{key:"debug",label:"\uD83D\uDD27 Debug Info",children:(0,t.jsx)(w,{pumpIndex:B,oldCalibValue:Y,latestHoldingValue:eh,actualMlValue:ei,isCalibrationInProgress:et,isCalibProgressFinished:ex})}]})]}),(0,t.jsx)(h.Z,{title:"Bơm đầy ống",open:U,onOk:()=>{R(!1),G(null)},onCancel:()=>{R(!1),G(null)},maskClosable:!1,okText:"X\xe1c nhận",okButtonProps:{type:"default"},cancelText:"Huỷ",cancelButtonProps:{hidden:!0},afterOpenChange:e=>{e?(G("fulfill"),er(e=>e+1)):G(null)},children:(0,t.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:16,marginBottom:32},children:[(0,t.jsx)("div",{style:{padding:8,backgroundColor:"#f6ffed",borderRadius:4},children:(0,t.jsx)("p",{style:{margin:0,fontSize:12,color:"#52c41a",fontWeight:"bold"},children:"\uD83D\uDCA1 Hướng dẫn: Nhập gi\xe1 trị lớn (v\xed dụ: 1000ml) v\xe0 bật bơm để đầy ống dẫn"})}),(0,t.jsx)(u.Z,{functionItem:A.digitCoil,setLatestDigitValue:eu},"fulfill-holding-".concat(eo)),(0,t.jsx)(g.Z,{functionItem:A.onOffCoil,setResponseStatusOfOnOffControl:F})]})}),(0,t.jsx)(h.Z,{title:"Tiến h\xe0nh hiệu chuẩn",open:K,onOk:()=>{$(!1),G(null)},onCancel:()=>{$(!1),G(null)},maskClosable:!1,okText:"X\xe1c nhận",okButtonProps:{type:"default"},cancelText:"Huỷ",cancelButtonProps:{hidden:!0},afterOpenChange:e=>{e?(G("calibration"),er(e=>e+1)):G(null)},children:(0,t.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:16,marginBottom:32},children:[(0,t.jsx)("div",{style:{padding:8,backgroundColor:"#fff7e6",borderRadius:4},children:(0,t.jsx)("p",{style:{margin:0,fontSize:12,color:"#d46b08",fontWeight:"bold"},children:"⚠️ Lưu \xfd: Kiểm tra v\xe0 điều chỉnh c\xe1c th\xf4ng số trước khi bắt đầu hiệu chuẩn"})}),(0,t.jsx)(u.Z,{functionItem:A.digitCoil,setLatestDigitValue:eu},"holding-".concat(eo)),E&&(0,t.jsx)(u.Z,{functionItem:E,setLatestDigitValue:e=>ee(e)},"calib-".concat(eo)),(0,t.jsx)(g.Z,{functionItem:A.onOffCoil,setResponseStatusOfOnOffControl:V})]})})]},S):(0,t.jsx)("p",{children:"Đang tải dữ liệu !"})},O=n(2569),A=n(8713);let{Title:E,Paragraph:M,Text:N}=C.default;var P=()=>{let e=[{title:"Bơm đầy ống",description:"Thiết lập gi\xe1 trị lớn v\xe0 bật bơm để đầy ống dẫn",content:(0,t.jsxs)("div",{children:[(0,t.jsx)(M,{children:"• Nhập gi\xe1 trị lớn (v\xed dụ: 1000ml) v\xe0o DigitControl"}),(0,t.jsx)(M,{children:"• Bật OnOffControl để chạy bơm"}),(0,t.jsx)(M,{children:"• Quan s\xe1t v\xe0 tắt bơm khi thấy ống đ\xe3 đầy"})]})},{title:"Thiết lập th\xf4ng số hiệu chuẩn",description:"Kiểm tra v\xe0 điều chỉnh c\xe1c th\xf4ng số trước khi hiệu chuẩn",content:(0,t.jsxs)("div",{children:[(0,t.jsxs)(M,{children:["• ",(0,t.jsx)(N,{strong:!0,children:"Lưu lượng bơm:"})," Nhập gi\xe1 trị cần hiệu chuẩn (v\xed dụ: 100ml)"]}),(0,t.jsxs)(M,{children:["• ",(0,t.jsx)(N,{strong:!0,children:"Hệ số hiệu chuẩn cũ:"})," Kiểm tra v\xe0 c\xf3 thể điều chỉnh nếu cần"]}),(0,t.jsx)(M,{children:"• Bật bơm v\xe0 quan s\xe1t qu\xe1 tr\xecnh"}),(0,t.jsxs)(M,{children:["• ",(0,t.jsx)(N,{strong:!0,children:"T\xf9y chọn:"})," C\xf3 thể nhập kết quả ngay khi đo xong hoặc chờ hết thời gian"]}),(0,t.jsx)(M,{children:"• Đo lượng thực tế ra được"})]})},{title:"Nhập kết quả đo",description:"Nhập lượng thực tế đo được v\xe0o hệ thống",content:(0,t.jsxs)("div",{children:[(0,t.jsx)(M,{children:"• Nhập ch\xednh x\xe1c lượng ml thực tế đo được"}),(0,t.jsx)(M,{children:'• Nhấn "Lưu" để gửi gi\xe1 trị l\xean hệ thống'})]})},{title:"T\xednh to\xe1n hệ số mới",description:"Hệ thống tự động t\xednh to\xe1n v\xe0 cập nhật hệ số hiệu chuẩn",content:(0,t.jsx)("div",{children:(0,t.jsx)(M,{children:'• Nhấn "T\xednh to\xe1n v\xe0 cập nhật hệ số hiệu chuẩn"'})})}];return(0,t.jsxs)(T.Z,{title:(0,t.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:8},children:[(0,t.jsx)(A.Z,{}),(0,t.jsx)("span",{children:"Hướng dẫn hiệu chuẩn bơm"})]}),style:{marginBottom:16},children:[(0,t.jsx)(_.Z,{message:"Lưu \xfd quan trọng",description:"Qu\xe1 tr\xecnh hiệu chuẩn cần thực hiện theo đ\xfang thứ tự c\xe1c bước. Đảm bảo đo ch\xednh x\xe1c lượng thực tế để c\xf3 kết quả hiệu chuẩn tốt nhất.",type:"warning",showIcon:!0,style:{marginBottom:16}}),(0,t.jsx)(O.Z,{direction:"vertical",size:"small",items:e.map((e,i)=>({title:e.title,description:e.description,status:"wait"}))}),(0,t.jsx)("div",{style:{marginTop:16},children:e.map((e,i)=>(0,t.jsx)(T.Z,{size:"small",title:"Bước ".concat(i+1,": ").concat(e.title),style:{marginBottom:8},children:e.content},i))})]})},H=n(4374),z=n(3779);let{Text:W,Paragraph:F}=C.default;var U=()=>{let{deviceId:e}=(0,l.Z)(),{run:i}=(0,p.Z)(),{subscribe:n,unsubscribe:r,connected:s}=(0,y.T)(),[c,a]=(0,o.useState)(1),[h,u]=(0,o.useState)(120),[g,f]=(0,o.useState)(100),[x,m]=(0,o.useState)(1.2),[b,v]=(0,o.useState)([]),C=async()=>{let n="CALIB_ACTUAL_ML_BOM_".concat(c);try{await i({device_id_thingsboard:e,method:"set_state",params:{[n]:h}}),console.log("✅ Sent ".concat(n," = ").concat(h)),alert("Đ\xe3 gửi ".concat(n," = ").concat(h))}catch(e){console.error("❌ Error sending actual ML:",e),alert("Lỗi khi gửi gi\xe1 trị thực tế")}},D=async()=>{let n="HOLDING_SETML_BOM_".concat(c);try{await i({device_id_thingsboard:e,method:"set_state",params:{[n]:g}}),console.log("✅ Sent ".concat(n," = ").concat(g)),alert("Đ\xe3 gửi ".concat(n," = ").concat(g))}catch(e){console.error("❌ Error sending SET ML:",e),alert("Lỗi khi gửi SET ML")}},L=async()=>{let n="HOLDING_CALIB_BOM_".concat(c);try{await i({device_id_thingsboard:e,method:"set_state",params:{[n]:x}}),console.log("✅ Sent ".concat(n," = ").concat(x)),alert("Đ\xe3 gửi ".concat(n," = ").concat(x))}catch(e){console.error("❌ Error sending old calib:",e),alert("Lỗi khi gửi hệ số cũ")}},k=async()=>{let n="CALCULATE_CALIB_BOM_".concat(c);try{await i({device_id_thingsboard:e,method:"set_state",params:{[n]:!0}}),console.log("✅ Sent ".concat(n," = true")),alert("Đ\xe3 gửi ".concat(n," = true"))}catch(e){console.error("❌ Error triggering calibration:",e),alert("Lỗi khi trigger hiệu chuẩn")}};return(0,t.jsxs)(T.Z,{title:"\uD83E\uDDEA Calibration Test Tool",style:{marginBottom:16},children:[(0,t.jsx)(_.Z,{message:"Test Tool",description:"C\xf4ng cụ n\xe0y gi\xfap test c\xe1c API calls v\xe0 MQTT subscription cho calibration",type:"info",showIcon:!0,style:{marginBottom:16}}),(0,t.jsxs)(H.Z,{direction:"vertical",style:{width:"100%"},children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(W,{strong:!0,children:"Device ID: "}),(0,t.jsx)(W,{code:!0,children:e||"Chưa c\xf3"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(W,{strong:!0,children:"MQTT Connected: "}),(0,t.jsx)(W,{style:{color:s?"green":"red"},children:s?"✅ Connected":"❌ Disconnected"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(W,{strong:!0,children:"Test Pump Index: "}),(0,t.jsx)(z.Z,{type:"number",value:c,onChange:e=>a(Number(e.target.value)),style:{width:100,marginLeft:8},min:1,max:14})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(W,{strong:!0,children:"Test SET ML: "}),(0,t.jsx)(z.Z,{type:"number",value:g,onChange:e=>f(Number(e.target.value)),style:{width:100,marginLeft:8}})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(W,{strong:!0,children:"Test Old Calib: "}),(0,t.jsx)(z.Z,{type:"number",step:"0.01",value:x,onChange:e=>m(Number(e.target.value)),style:{width:100,marginLeft:8}})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(W,{strong:!0,children:"Test Actual ML: "}),(0,t.jsx)(z.Z,{type:"number",value:h,onChange:e=>u(Number(e.target.value)),style:{width:100,marginLeft:8}})]}),(0,t.jsxs)(H.Z,{wrap:!0,children:[(0,t.jsx)(d.ZP,{type:"primary",onClick:D,children:"Test Send SET ML"}),(0,t.jsx)(d.ZP,{type:"primary",onClick:L,children:"Test Send Old Calib"}),(0,t.jsx)(d.ZP,{type:"primary",onClick:C,children:"Test Send Actual ML"}),(0,t.jsx)(d.ZP,{type:"primary",onClick:k,children:"Test Trigger Calibration"}),(0,t.jsx)(d.ZP,{onClick:()=>{if(!e){alert("Kh\xf4ng c\xf3 deviceId");return}let i=(0,j.v)(e);console.log("Subscribing to topic:",i);let t=n([i],e=>{console.log("\uD83D\uDCE8 MQTT Message received:",e),v(i=>["".concat(new Date().toLocaleTimeString(),": ").concat(e),...i.slice(0,4)]);try{let i=JSON.parse(e);if(Array.isArray(i)){let e=i.filter(e=>e.key&&e.key.startsWith("CALCULATE_CALIB_BOM_"));e.length>0&&console.log("\uD83D\uDD27 Calibration flags found:",e)}}catch(e){console.error("Error parsing MQTT message:",e)}});console.log("Subscription IDs:",t),setTimeout(()=>{r(t),console.log("Auto unsubscribed from test")},3e4)},children:"Test MQTT Subscription"})]}),b.length>0&&(0,t.jsxs)("div",{children:[(0,t.jsx)(W,{strong:!0,children:"Recent MQTT Messages:"}),(0,t.jsx)("div",{style:{backgroundColor:"#f5f5f5",padding:8,borderRadius:4,maxHeight:200,overflow:"auto"},children:b.map((e,i)=>(0,t.jsx)("div",{style:{fontSize:11,fontFamily:"monospace"},children:e},i))})]}),(0,t.jsx)(_.Z,{message:"Expected Keys",description:(0,t.jsxs)("div",{children:[(0,t.jsxs)(F,{style:{margin:0},children:[(0,t.jsxs)(W,{code:!0,children:["HOLDING_SETML_BOM_",c]})," - Lưu lượng bơm"]}),(0,t.jsxs)(F,{style:{margin:0},children:[(0,t.jsxs)(W,{code:!0,children:["HOLDING_CALIB_BOM_",c]})," - Hệ số hiệu chuẩn cũ"]}),(0,t.jsxs)(F,{style:{margin:0},children:[(0,t.jsxs)(W,{code:!0,children:["CALIB_ACTUAL_ML_BOM_",c]})," - Gi\xe1 trị thực tế đo được"]}),(0,t.jsxs)(F,{style:{margin:0},children:[(0,t.jsxs)(W,{code:!0,children:["CALCULATE_CALIB_BOM_",c]})," - Trigger t\xednh to\xe1n"]})]}),type:"info"})]})]})},R=()=>{var e,i,n;let{functionListForCalibration:r,functionListForControl:s}=(0,l.Z)(),[c,d]=(0,o.useState)(null),[h,u]=(0,o.useState)(null);(0,o.useEffect)(()=>{var e,i;if(!s||!r)return;let n=null===(e=s.find(e=>"tb1"===e.identifier))||void 0===e?void 0:e.children;if(!n||0===n.length)return;let t=null===(i=n.find(e=>"Bơm m\xf4i trường"===e.label))||void 0===i?void 0:i.children;t&&0!==t.length&&u(t)},[s]);let[g,f]=(0,o.useState)(null);return((0,o.useEffect)(()=>{var e,i;if(!s||!r)return;let n=null===(e=s.find(e=>"config"===e.identifier))||void 0===e?void 0:e.children;if(!n||0===n.length)return;let t=null===(i=n.find(e=>"Calib bơm m\xf4i trường"===e.label))||void 0===i?void 0:i.children;t&&0!==t.length&&f(t)},[]),h&&0!==h.length&&r&&0!==r.length)?(0,t.jsxs)("div",{style:{display:"flex",flexDirection:"column",padding:16},children:[(0,t.jsx)("p",{style:{fontSize:24,fontWeight:"bold"},children:"Hiệu chuẩn bơm định lượng"}),(0,t.jsx)(a.Z,{style:{marginBottom:16},items:[{key:"guide",label:"\uD83D\uDCD6 Hướng dẫn sử dụng",children:(0,t.jsx)(P,{})},...Z("calibrationTestTool")?[{key:"test",label:"\uD83E\uDDEA Test Tool",children:(0,t.jsx)(U,{})}]:[]]}),(0,t.jsx)("p",{style:{fontSize:18,fontWeight:"bold",marginBottom:16},children:"Danh s\xe1ch bơm hiệu chuẩn"}),(0,t.jsx)("div",{style:{display:"flex",flexDirection:"row",flexWrap:"wrap",gap:16},children:null==r?void 0:null===(n=r[0])||void 0===n?void 0:null===(i=n.children)||void 0===i?void 0:null===(e=i[0])||void 0===e?void 0:e.children.map((e,i)=>(0,t.jsx)(B,{indexOfPump:i,functionItem:e,functionsForControl:{onOffCoil:h[2*i],digitCoil:h[2*i+1]},functionForOldCalibration:g[i+1],isThereAnyCalibration:c,setIsThereAnyCalibration:d},i))})]}):(0,t.jsx)("p",{children:"Đang tải dữ liệu"})};function q(){return(0,t.jsx)(R,{})}}},function(e){e.O(0,[478,452,872,456,670,382,858,911,888,774,179],function(){return e(e.s=999)}),_N_E=e.O()}]);