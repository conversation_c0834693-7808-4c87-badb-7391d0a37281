"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[911],{8198:function(e,n,t){var i=t(4246),l=t(4478),o=t(6316),r=t(7378),a=t(9739),d=t(1584),s=t(1421),u=t(1624),c=t(9960),f=t(22);let v={width:"100%",display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"space-between",gap:8,border:"1px solid rgb(230,230,230)",padding:8,borderRadius:8,backgroundColor:"#fff"};n.Z=e=>{let{functionItem:n,setLatestDigitValue:t}=e,{deviceData:g,deviceId:h}=(0,a.Z)(),{data:m,loading:p,run:x}=(0,d.Z)(),[b,y]=(0,r.useState)(!1),{run:w,loading:C}=(0,s.Z)(),{handleMessage:N,client:S}=(0,u.T)(),[j,k]=(0,r.useState)(0);function _(){g.name&&n.identifier&&(console.log("Start to get new data"),x({deviceId:null==g?void 0:g.name,keys:[n.identifier]}))}(0,r.useEffect)(()=>{_()},[null==g?void 0:g.name,n.identifier]),(0,r.useEffect)(()=>{var e;if(!(null==g?void 0:g.name)||!n.identifier)return;let i=(null==m?void 0:null===(e=m.data)||void 0===e?void 0:e[null==n?void 0:n.identifier])||[];if(console.log("latestDataWithFunctionKey",i),i.length>0){let e=i[i.length-1];e&&(k(Number(null==e?void 0:e.value)),t&&t(Number(e.value)))}(null==m?void 0:m.data)&&y(!0)},[m,null==g?void 0:g.name,n.identifier]),(0,r.useEffect)(()=>{b&&S.on("message",(e,i)=>{if(e===(0,c.v)(h)){console.log("DIGIT CONTROL: Received MQTT message on topic ".concat(e,":"),i.toString());try{let e=JSON.parse(i.toString());if(Array.isArray(e)){let i=e.filter(e=>e.key===n.identifier);if(i.length>0){let e=i[i.length-1];k(Number(null==e?void 0:e.value)),t&&t(Number(e.value))}}}catch(e){console.error("MQTT message error:",e)}}})},[N,null==g?void 0:g.name,n.identifier]);let E=async e=>{let t=null==g?void 0:g.name;t&&n.identifier&&(await w({device_id_thingsboard:t,method:"set_state",params:{[n.identifier]:e}}).then(()=>{k(0)}),_())};return(0,i.jsx)("div",{style:v,children:(0,i.jsx)(l.default,{layout:"vertical",style:{width:"100%"},children:(0,i.jsxs)("div",{style:{width:"100%",display:"flex",flexDirection:"row",justifyContent:"space-between",flexWrap:"wrap",gap:"8px",alignItems:"end"},children:[(0,i.jsx)(l.default.Item,{style:{width:"calc(100% - 70px)",marginBottom:0},name:n.name,label:(0,i.jsx)("p",{style:{color:"rgb(100,100,100)",margin:0},children:n.label}),required:!0,children:(0,i.jsx)(f.Z,{style:{width:"100%"},onChange:e=>k(e),placeholder:null==j?void 0:j.toString()})}),(0,i.jsx)(l.default.Item,{style:{marginBottom:0},children:(0,i.jsx)(o.ZP,{type:"primary",style:{width:"60px"},onClick:()=>E(j),children:"OK"})})]})})})}},6502:function(e,n,t){var i=t(4246),l=t(7378),o=t(4717),r=t(2754),a=t(2450),d=t(5032),s=t(1584),u=t(9739),c=t(1421),f=t(1624),v=t(9960);let g={width:"100%",display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"space-between",gap:8,border:"1px solid rgb(230,230,230)",padding:8,borderRadius:8,backgroundColor:"#fff"};n.Z=e=>{let{functionItem:n,readonly:t,setResponseStatusOfOnOffControl:h}=e,{languageData:m}=(0,o.Z)(),{deviceData:p,deviceId:x}=(0,u.Z)(),{data:b,loading:y,run:w}=(0,s.Z)(),[C,N]=(0,l.useState)(!1),{run:S,loading:j}=(0,c.Z)(),{handleMessage:k,client:_}=(0,f.T)(),[E,Z]=(0,l.useState)(!1),T=e=>"true"===String(e);function I(){p.name&&n.identifier&&(console.log("Start to get new data"),w({deviceId:null==p?void 0:p.name,keys:[n.identifier]}))}(0,l.useEffect)(()=>{I()},[null==p?void 0:p.name,n.identifier]),(0,l.useEffect)(()=>{var e;if(!(null==p?void 0:p.name)||!n.identifier)return;let t=(null==b?void 0:null===(e=b.data)||void 0===e?void 0:e[null==n?void 0:n.identifier])||[];if(console.log("latestDataWithFunctionKey",t),t.length>0){let e=t[t.length-1];e&&Z(T(null==e?void 0:e.value))}(null==b?void 0:b.data)&&N(!0)},[b,null==p?void 0:p.name,n.identifier]),(0,l.useEffect)(()=>{C&&_.on("message",(e,t)=>{if(e===(0,v.v)(x)){console.log("ON OFF CONTROL: Received MQTT message on topic ".concat(e,":"),t.toString());try{let e=JSON.parse(t.toString());if(Array.isArray(e)){let t=e.filter(e=>e.key===n.identifier);if(t.length>0){let e=t[t.length-1];e&&Z(T(null==e?void 0:e.value))}}}catch(e){console.error("MQTT message error:",e)}}})},[C,k,null==p?void 0:p.name,n.identifier]);let R=async e=>{let t=null==p?void 0:p.name;t&&n.identifier&&(await S({device_id_thingsboard:t,method:"set_state",params:{[n.identifier]:e}}),I())};return(0,l.useEffect)(()=>{h&&h(E)},[E]),(0,i.jsxs)("div",{style:g,children:[(0,i.jsxs)("div",{style:{display:"flex",flexDirection:"row",alignItems:"center",gap:8},children:[n.icon_url?(0,i.jsx)("img",{height:"24px",src:(0,r.rH)("api/v2/file/download?file_url="+n.icon_url),onError:()=>(0,i.jsx)(a.Z,{})}):(0,i.jsx)(a.Z,{}),(0,i.jsx)("p",{style:{margin:0,color:"rgb(100,100,100)"},children:n.label})]}),(0,i.jsx)(d.Z,{disabled:t,value:E,onChange:R,checked:E,loading:j||y,checkedChildren:n.data_on_text?n.data_on_text:m["common.control.switch.on"],unCheckedChildren:n.data_off_text?n.data_off_text:m["common.control.switch.off"],style:{minWidth:"60px"}})]})}},22:function(e,n,t){var i=t(4246),l=t(7378),o=t(6872),r=t(1117),a=t.n(r);t(8707);let d=l.forwardRef((e,n)=>{let[t,r]=(0,l.useState)(""),[d,s]=(0,l.useState)(!1),u=(0,l.useRef)(null),c=(0,l.useRef)(null),f=(0,l.useRef)(null);(0,l.useEffect)(()=>{n&&(n.current=c.current)},[n]),(0,l.useEffect)(()=>{void 0!==e.value&&null!==e.value&&String(e.value)!==t&&r(String(e.value))},[e.value]);let v=n=>{console.log("input: ",n);let t=Number(n),i=""!==n&&!isNaN(t);if(n&&i)r(n),e.onChange&&e.onChange(t);else if(r(void 0),e.onChange){var l;null===(l=e.onChange)||void 0===l||l.call(e,void 0)}},g=()=>{s(!0),setTimeout(()=>{var e;null===(e=u.current)||void 0===e||e.setInput(t)},100)};return(0,l.useEffect)(()=>{let e=e=>{var n,t;!c.current||(null===(n=c.current.inputElement)||void 0===n?void 0:n.contains(e.target))||(null===(t=f.current)||void 0===t?void 0:t.contains(e.target))||s(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(o.Z,{...e,ref:c,value:""===t||isNaN(Number(t))?void 0:Number(t),onChange:n=>{let t=Number(n);""!==n&&!isNaN(t)&&(r(("string"==typeof n&&n.includes("."),t.toString())),e.onChange&&e.onChange(t))},onFocus:g,onBlur:()=>{var n,i;let l=null==t?void 0:t.replace(/[^0-9.]/g,""),o=Number(l);!l||isNaN(o)?(r(void 0),null===(n=e.onChange)||void 0===n||n.call(e,void 0)):(r(l),null===(i=e.onChange)||void 0===i||i.call(e,o))},onClick:g,precision:void 0!==e.precision?e.precision:2,step:void 0!==e.step?e.step:.01}),d&&(0,i.jsxs)("div",{ref:f,className:"rsk-container",style:{position:"fixed",bottom:0,left:0,width:"100%",background:"rgba(255,255,255,0.2)",boxShadow:"0 -2px 8px rgba(0,0,0,0.15)",zIndex:1e3},children:[(0,i.jsx)("div",{style:{padding:"8px 16px",borderBottom:"1px solid #e8e8e8",fontSize:"16px",fontWeight:"bold",background:"#fafafa",whiteSpace:"nowrap",overflowX:"auto"},children:t||"\xa0"}),(0,i.jsx)(a(),{ref:u,onChange:v,onKeyPress:e=>{if("{bksp}"===e){v((null==t?void 0:t.slice(0,-1))||"");return}if("{enter}"===e){var n;s(!1),null===(n=c.current)||void 0===n||n.blur();return}"."===e&&(t||"").includes(".")||v((t||"")+e)},layout:{default:["1 2 3","4 5 6","7 8 9",". 0 {bksp}","{enter}"]},display:{"{bksp}":"⌫","{enter}":"⏎"}})]})]})});n.Z=d},1421:function(e,n,t){var i=t(9341),l=t(465);n.Z=function(){let{onSuccess:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(0,l.Q)(i.A2,{onError(e,n){},onSuccess(n,t){null==e||e(n)},manual:!0})}},1584:function(e,n,t){var i=t(9341),l=t(465);n.Z=function(){let{onSuccess:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(0,l.Q)(i.$U,{onError(e,n){},onSuccess(n,t){null==e||e(n)}})}}}]);