"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[563],{22:function(e,t,n){var i=n(4246),l=n(7378),r=n(6872),a=n(1117),s=n.n(a);n(8707);let o=l.forwardRef((e,t)=>{let[n,a]=(0,l.useState)(""),[o,d]=(0,l.useState)(!1),c=(0,l.useRef)(null),u=(0,l.useRef)(null),h=(0,l.useRef)(null);(0,l.useEffect)(()=>{t&&(t.current=u.current)},[t]),(0,l.useEffect)(()=>{void 0!==e.value&&null!==e.value&&String(e.value)!==n&&a(String(e.value))},[e.value]);let p=t=>{console.log("input: ",t);let n=Number(t),i=""!==t&&!isNaN(n);if(t&&i)a(t),e.onChange&&e.onChange(n);else if(a(void 0),e.onChange){var l;null===(l=e.onChange)||void 0===l||l.call(e,void 0)}},m=()=>{d(!0),setTimeout(()=>{var e;null===(e=c.current)||void 0===e||e.setInput(n)},100)};return(0,l.useEffect)(()=>{let e=e=>{var t,n;!u.current||(null===(t=u.current.inputElement)||void 0===t?void 0:t.contains(e.target))||(null===(n=h.current)||void 0===n?void 0:n.contains(e.target))||d(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(r.Z,{...e,ref:u,value:""===n||isNaN(Number(n))?void 0:Number(n),onChange:t=>{let n=Number(t);""!==t&&!isNaN(n)&&(a(("string"==typeof t&&t.includes("."),n.toString())),e.onChange&&e.onChange(n))},onFocus:m,onBlur:()=>{var t,i;let l=null==n?void 0:n.replace(/[^0-9.]/g,""),r=Number(l);!l||isNaN(r)?(a(void 0),null===(t=e.onChange)||void 0===t||t.call(e,void 0)):(a(l),null===(i=e.onChange)||void 0===i||i.call(e,r))},onClick:m,precision:void 0!==e.precision?e.precision:2,step:void 0!==e.step?e.step:.01}),o&&(0,i.jsxs)("div",{ref:h,className:"rsk-container",style:{position:"fixed",bottom:0,left:0,width:"100%",background:"rgba(255,255,255,0.2)",boxShadow:"0 -2px 8px rgba(0,0,0,0.15)",zIndex:1e3},children:[(0,i.jsx)("div",{style:{padding:"8px 16px",borderBottom:"1px solid #e8e8e8",fontSize:"16px",fontWeight:"bold",background:"#fafafa",whiteSpace:"nowrap",overflowX:"auto"},children:n||"\xa0"}),(0,i.jsx)(s(),{ref:c,onChange:p,onKeyPress:e=>{if("{bksp}"===e){p((null==n?void 0:n.slice(0,-1))||"");return}if("{enter}"===e){var t;d(!1),null===(t=u.current)||void 0===t||t.blur();return}"."===e&&(n||"").includes(".")||p((n||"")+e)},layout:{default:["1 2 3","4 5 6","7 8 9",". 0 {bksp}","{enter}"]},display:{"{bksp}":"⌫","{enter}":"⏎"}})]})]})});t.Z=o},508:function(e,t,n){var i=n(4246),l=n(7378),r=n(2096),a=n(4478),s=n(5475),o=n(6316),d=n(3295),c=n(3992),u=n(1671),h=n(4582),p=n(213),m=n(4899),g=n(5032),f=n(7693),x=n.n(f),v=n(9739),y=n(2754),j=n(2450),b=n(1629),_=n(1555),w=n(22);t.Z=e=>{var t,n,f;let{program:C,onClose:Z,start_date_of_plan:T,end_date_of_plan:k}=e,[H]=a.default.useForm(),{schedulePlans:S,setSchedulePlans:D,scheduleProgramTriggerImmediately:P}=(0,r.Z)(),[E,I]=(0,l.useState)([x()(C.start_date),x()(C.end_date)]),[N,V]=E,[W,B]=[x()(C.start_time,"HH:mm:ss"),x()(C.end_time,"HH:mm:ss")],[O,F]=(0,l.useState)([]);(0,l.useEffect)(()=>{(null==C?void 0:C.interval)&&F(C.interval.split(","))},[null==C?void 0:C.interval]);let{functionListForControl:R}=(0,v.Z)(),[z,L]=(0,l.useState)([]);(0,l.useEffect)(()=>{P&&L(P.enum_value.split(",").map(e=>({value:e.trim(),label:e.trim()})))},[P]);let q=async e=>{try{console.log("values: ",e);let t=Object.fromEntries(Object.entries(e.action||{}).map(e=>{let[t,n]=e;return"boolean"==typeof n?[t,String(n)]:"number"==typeof n||"string"==typeof n?[t,n]:[t,String(n)]})),n={id:C.id,name:e.name,start_time:e.start_time.format("HH:mm:ss"),end_time:e.start_time.add(e.time_running,"seconds").format("HH:mm:ss"),start_date:E[0].format("YYYY-MM-DD"),end_date:E[1].format("YYYY-MM-DD"),interval:e.interval.join(","),enable:e.enable?1:0,schedule_plan_id:C.schedule_plan_id,device_id:C.device_id,type:"",action:t};console.log("programToPush: ",n);let i=await (0,b.Fq)(n);if(null==i?void 0:i.statusOK){s.ZP.success("Chỉnh sửa chương tr\xecnh th\xe0nh c\xf4ng");let e=S.map(e=>e.name===C.schedule_plan_id?{...e,schedules:e.schedules.map(e=>{if(e.id===C.id){var t,n;return null==i?void 0:null===(n=i.responseData)||void 0===n?void 0:null===(t=n.result)||void 0===t?void 0:t.data}return e})}:e);D(e),Z()}}catch(e){s.ZP.error("C\xf3 lỗi xảy ra khi chỉnh sửa chương tr\xecnh ! Vui l\xf2ng thử lại")}};return console.log("program right now: ",C),console.log("intervalDays: ",O),(0,i.jsxs)(a.default,{layout:"vertical",form:H,style:{width:"100%"},children:[(0,i.jsxs)("div",{style:{zIndex:100,position:"fixed",bottom:24,right:24,display:"flex",justifyContent:"flex-end",gap:8,padding:8,background:"rgba(255, 255, 255, 0.5)",borderRadius:8,backdropFilter:"blur(5px)",border:"1px solid #ddd",boxShadow:"0px 0px 50px 2px rgba(0, 0, 0, 0.25)"},children:[(0,i.jsx)(o.ZP,{onClick:()=>Z(),children:"Hủy"}),(0,i.jsx)(o.ZP,{type:"primary",onClick:()=>q(H.getFieldsValue()),children:"Lưu"})]}),(0,i.jsx)(a.default.Item,{name:"interval",label:"\xc1p dụng cho c\xe1c thứ",rules:[{required:!0}],initialValue:O,children:(0,i.jsx)(d.Z.Group,{options:[{label:"Chủ Nhật",value:"0"},{label:"Thứ 2",value:"1"},{label:"Thứ 3",value:"2"},{label:"Thứ 4",value:"3"},{label:"Thứ 5",value:"4"},{label:"Thứ 6",value:"5"},{label:"Thứ 7",value:"6"}],onChange:e=>F(e)})}),(0,i.jsx)(c.Z,{gutter:[16,16],children:(0,i.jsx)(u.Z,{span:24,children:(0,i.jsx)(a.default.Item,{name:"name",label:"T\xean chương tr\xecnh",rules:[{required:!0}],layout:"vertical",initialValue:C.name,children:(0,i.jsx)(_.Z,{style:{width:"100%"}})})})}),(0,i.jsxs)(c.Z,{gutter:[16,16],children:[(0,i.jsx)(u.Z,{span:12,children:(0,i.jsx)(a.default.Item,{name:"start_time",label:"Thời gian bắt đầu",rules:[{required:!0}],layout:"vertical",initialValue:W,children:(0,i.jsx)(h.Z,{style:{width:"100%"}})})}),(0,i.jsx)(u.Z,{span:12,children:(0,i.jsx)(a.default.Item,{name:"time_running",initialValue:x()(C.end_time,"HH:mm:ss").diff(x()(C.start_time,"HH:mm:ss"),"second"),label:"Thời gian thực hiện (Gi\xe2y)",rules:[{required:!0}],layout:"vertical",children:(0,i.jsx)(w.Z,{style:{width:"100%"}})})})]}),(0,i.jsx)(c.Z,{gutter:[16,16],children:(0,i.jsx)(u.Z,{span:24,children:(0,i.jsx)(a.default.Item,{rules:[{required:!0}],label:"Ng\xe0y thực hiện",initialValue:[N,V],children:(0,i.jsx)(p.default.RangePicker,{style:{width:"100%"},onChange:e=>I(e),defaultValue:[N,V],disabledDate:e=>{let t=x()().startOf("day"),n=x()(T),i=t.isBefore(n)?n:t,l=x()(k);return e&&(e<i||e>l)}},"date_range_picker")})})}),(0,i.jsx)(c.Z,{gutter:[16,16],children:(0,i.jsx)(u.Z,{span:12,children:(0,i.jsx)(a.default.Item,{name:["action","env_enum"],rules:[{required:!0}],label:"M\xe3 m\xf4i trường",initialValue:null==C?void 0:null===(t=C.action)||void 0===t?void 0:t.env_enum,children:(0,i.jsx)(m.default,{placeholder:"Chọn m\xe3 m\xf4i trường",style:{width:"100%"},options:z})})})}),(0,i.jsx)(u.Z,{span:24,style:{marginTop:32},children:null===(f=R.find(e=>"tb1"===e.identifier))||void 0===f?void 0:null===(n=f.children)||void 0===n?void 0:n.map(e=>{var t;return 0===e.children.length?null:(0,i.jsxs)(c.Z,{style:{marginBottom:32},children:[(0,i.jsx)("p",{style:{margin:0,fontSize:16,fontWeight:"bold"},children:e.label}),(0,i.jsx)(u.Z,{span:24,style:{marginTop:8},children:null==e?void 0:null===(t=e.children)||void 0===t?void 0:t.map(e=>(0,i.jsx)(c.Z,{gutter:[16,16],style:{borderTop:"1px solid #ddd"},children:(0,i.jsx)(u.Z,{span:24,children:(0,i.jsxs)(a.default.Item,{style:{marginBottom:0},name:["action",e.identifier],initialValue:C.action[e.identifier],layout:"horizontal",labelCol:{span:12,style:{textAlign:"left"}},wrapperCol:{span:12,style:{textAlign:"right"}},colon:!1,label:(0,i.jsxs)("div",{style:{display:"flex",flexDirection:"row",alignItems:"center",content:""},children:[e.icon_url?(0,i.jsx)("img",{height:"24px",src:(0,y.rH)("api/v2/file/download?file_url="+e.icon_url),onError:()=>(0,i.jsx)(j.Z,{})}):(0,i.jsx)(j.Z,{}),(0,i.jsx)("p",{style:{margin:0,marginLeft:8},children:e.label})]}),children:["Bool"===e.data_type&&(0,i.jsx)(g.Z,{style:{width:40},checked:"true"===H.getFieldValue(["action",e.identifier]),onChange:t=>{H.setFieldValue(["action",e.identifier],t.toString())}}),"Value"===e.data_type&&(0,i.jsx)(w.Z,{defaultValue:"number"==typeof C.action[e.identifier]?C.action[e.identifier]:0,style:{width:200,marginTop:4,marginBottom:4}})]})})},e.identifier))})]},e.label)})})]})}},3563:function(e,t,n){n.d(t,{U:function(){return b},Z:function(){return C}});var i=n(4246),l=n(7378),r=n(5475),a=n(3992),s=n(1671),o=n(2094),d=n(5032),c=n(2096),u=n(1629),h=e=>{let{program:t,plan_id:n,programActivated:l,setProgramActivated:a}=e;console.log("program with id"+t.id+"is: "+l);let{schedulePlans:s,setSchedulePlans:o}=(0,c.Z)(),h=async e=>{let i={...t,enable:e?1:0},l=await (0,u.Fq)(i);(null==l?void 0:l.statusOK)&&(o(s.map(e=>e.name===n?{...e,schedules:e.schedules.map(e=>e.id===i.id?i:e)}:e)),r.ZP.success("".concat(e?"K\xedch hoạt":"Tắt"," chương tr\xecnh th\xe0nh c\xf4ng: ").concat(i.name)),console.log(i),a(e?1:0))};return(0,i.jsx)(d.Z,{style:{backgroundColor:l?"#45c3a1":"#ccc"},checked:0!==l,onChange:e=>h(e)})},p=n(9739),m=n(7767),g=n(6316),f=n(1216),x=e=>{let{plan_id:t,program_id:n,enable_program:a}=e,[s,o]=(0,l.useState)(!1),{schedulePlans:d,setSchedulePlans:h}=(0,c.Z)(),p=async()=>{let e=await (0,u.z1)(n);(null==e?void 0:e.statusOK)&&(h(d.map(e=>e.name===t?{...e,schedules:e.schedules.filter(e=>e.id!==n)}:e)),r.ZP.success("X\xf3a chương tr\xecnh th\xe0nh c\xf4ng")),o(!1)};return(0,i.jsxs)("div",{children:[(0,i.jsx)(g.ZP,{icon:(0,i.jsx)(m.Z,{}),danger:!0,onClick:()=>{if(1==a){r.ZP.error("Chương tr\xecnh đang được k\xedch hoạt! Vui l\xf2ng tắt trước");return}o(!0)},style:{borderRadius:8}}),(0,i.jsx)(f.Z,{open:s,onOk:p,onCancel:()=>o(!1),okButtonProps:{type:"primary",title:"X\xf3a",danger:!0},okText:"X\xf3a",cancelButtonProps:{type:"default",title:"Hủy"},cancelText:"Hủy",children:(0,i.jsx)("p",{style:{textAlign:"center",fontWeight:"bold",fontSize:"14px"},children:"Bạn c\xf3 chắc chắn muốn x\xf3a chương tr\xecnh n\xe0y?"})})]})},v=n(508),y=n(7693),j=n.n(y);let b=(e,t)=>{let n=new Date().toISOString().split("T")[0],i=new Date("".concat(n,"T").concat(e));return(new Date("".concat(n,"T").concat(t)).getTime()-i.getTime())/1e3},_=(e,t)=>(0,i.jsx)("div",{style:{height:"40px",borderRadius:16,border:"1px solid #ddd",padding:8,background:t?"#45c3a1":"#ddd"},children:(0,i.jsx)("p",{style:{margin:0,color:"white",fontWeight:"bold"},children:e})}),w=(e,t)=>e.split(",").map(e=>{switch(e){case"0":return _("Chủ nhật",t);case"1":return _("Thứ 2",t);case"2":return _("Thứ 3",t);case"3":return _("Thứ 4",t);case"4":return _("Thứ 5",t);case"5":return _("Thứ 6",t);case"6":return _("Thứ 7",t);default:return""}});var C=e=>{var t;let{program:n,start_date_of_plan:d,end_date_of_plan:c}=e,[u,m]=(0,l.useState)(n.enable?1:0),{functionListForControl:g}=(0,p.Z)();console.log("functionListForControl: ",g);let[f,y]=(0,l.useState)(!1);return(0,i.jsxs)("div",{style:{display:"flex",flexDirection:"column",boxShadow:"0px 0px 8px rgba(0, 0, 0, 0.1)",border:"1px solid #eee",padding:8,borderRadius:16,background:u?"linear-gradient(to bottom left,rgb(152, 251, 158), 5%, #fff)":"#fff"},children:[(0,i.jsxs)("div",{style:{display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"space-between"},children:[(0,i.jsxs)("div",{style:{display:"flex",flexDirection:"row",alignItems:"center",gap:8},children:[(0,i.jsx)("div",{style:{height:8,width:8,borderRadius:"50%",backgroundColor:u?"#45c3a1":"gray"}}),(0,i.jsx)("p",{style:{fontSize:16,fontWeight:"bold",margin:0,color:u?"#45c3a1":"gray",cursor:"pointer"},onClick:()=>{0===u?y(!0):r.ZP.error("Chương tr\xecnh n\xe0y đang được k\xedch hoạt ! Vui l\xf2ng tắt trước")},children:n.name||"..."})]}),(0,i.jsx)(x,{plan_id:n.schedule_plan_id,program_id:n.id,enable_program:n.enable})]}),(0,i.jsxs)("div",{style:{display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"space-between",marginTop:16,marginBottom:16},children:[(0,i.jsxs)("div",{style:{margin:0,color:u?"#45c3a1":"gray",fontSize:13,display:"flex",flexDirection:"row",alignItems:"center",gap:8},children:[(0,i.jsx)("p",{style:{margin:0},children:" Lịch thực hiện: "}),w(n.interval,0!==u).map(e=>e)]}),(0,i.jsx)(h,{program:n,plan_id:n.schedule_plan_id,programActivated:u?1:0,setProgramActivated:m})]}),(0,i.jsxs)("div",{style:{color:u?"#45c3a1":"gray",display:"flex",flexDirection:"column"},children:[(0,i.jsxs)(a.Z,{gutter:[32,32],children:[(0,i.jsxs)(s.Z,{span:12,style:{display:"flex",flexDirection:"row",gap:32},children:[(0,i.jsx)("p",{style:{margin:0},children:"Thời điểm:"}),(0,i.jsxs)("p",{style:{margin:0,fontWeight:"bold"},children:[j()(n.start_time,"HH:mm:ss").format("HH:mm:ss")," -"," ",j()(n.end_time,"HH:mm:ss").format("HH:mm:ss")]})]}),(0,i.jsxs)(s.Z,{span:12,style:{display:"flex",flexDirection:"row",justifyContent:"space-between"},children:[(0,i.jsx)("p",{style:{margin:0},children:"Tổng thời gian: "}),(0,i.jsxs)("div",{style:{display:"flex",flexDirection:"row",justifyContent:"space-between",width:"100px"},children:[(0,i.jsx)("p",{style:{margin:0,fontWeight:"bold"},children:b(n.start_time,n.end_time)}),(0,i.jsx)("p",{style:{margin:0},children:"Gi\xe2y"})]})]})]}),(0,i.jsx)(a.Z,{gutter:[16,16],children:(0,i.jsx)(s.Z,{span:24,children:(0,i.jsxs)("p",{children:["M\xe3 m\xf4i trường: \xa0\xa0"," ",(0,i.jsx)("strong",{children:(null==n?void 0:null===(t=n.action)||void 0===t?void 0:t.env_enum)||""})]})})}),(0,i.jsx)("div",{style:{width:"100%",borderBottom:"1px solid #eee",marginTop:8,marginBottom:16}}),(e=>{var t;let n=null===(t=g.find(e=>"tb1"===e.identifier))||void 0===t?void 0:t.children,l={label:"",unit:""},r=e=>(null==n||n.forEach(t=>{var n;null==t||null===(n=t.children)||void 0===n||n.forEach(t=>{(null==t?void 0:t.identifier)===e&&(l.label=null==t?void 0:t.label,l.unit=null==t?void 0:t.unit)})}),l);return(0,i.jsx)("div",{style:{width:"100%",display:"flex",flexDirection:"column"},children:Object.entries(e).map(e=>{let[t,n]=e;return"number"!=typeof n&&"true"!==String(n)||"number"==typeof n&&0===n?null:(0,i.jsxs)("div",{style:{display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"space-between",gap:8},children:[(0,i.jsx)("p",{style:{margin:0},children:r(t).label}),(0,i.jsxs)("div",{style:{display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"space-between",width:"100px"},children:[(0,i.jsx)("p",{style:{margin:0,fontWeight:0!==n?"bold":"normal"},children:"false"===n?"Tắt":"true"===n?"Bật":n}),(0,i.jsx)("p",{style:{margin:0},children:l.unit})]})]},t)})})})(n.action)]}),(0,i.jsx)(o.Z,{title:"Chỉnh sửa chương tr\xecnh",open:f,onClose:()=>{y(!1)},width:"70%",children:(0,i.jsx)(v.Z,{program:n,onClose:()=>{y(!1)},start_date_of_plan:d,end_date_of_plan:c})}),(0,i.jsx)("div",{style:{width:"100%",height:80}})]},n.id)}},1629:function(e,t,n){n.d(t,{Fq:function(){return c},Jn:function(){return d},Tx:function(){return a},YL:function(){return s},nb:function(){return r},uI:function(){return o},z1:function(){return u}});var i=n(465),l=n(2754);let r=async e=>{let t=new URLSearchParams({filters:JSON.stringify([["iot_schedule_plan","device_id","like",e]])});return(await (0,i.W)((0,l.rH)("api/v2/schedulePlan?".concat(t.toString())),{method:"GET",headers:{"Content-Type":"application/json"}})).responseData},a=async e=>(0,i.W)((0,l.rH)("api/v2/schedulePlan"),{method:"POST",headers:{"Content-Type":"application/json"},data:e}),s=async e=>(0,i.W)((0,l.rH)("api/v2/schedulePlan/ver2"),{method:"PUT",headers:{"Content-Type":"application/json"},data:e}),o=async e=>(0,i.W)((0,l.rH)("api/v2/schedulePlan?name=".concat(e)),{method:"DELETE",headers:{"Content-Type":"application/json"}}),d=async e=>(0,i.W)((0,l.rH)("api/v2/schedule/ver2"),{method:"POST",headers:{"Content-Type":"application/json"},data:e}),c=async e=>(0,i.W)((0,l.rH)("api/v2/schedule/ver2"),{method:"PUT",headers:{"Content-Type":"application/json"},data:e}),u=async e=>(0,i.W)((0,l.rH)("api/v2/schedule?name=".concat(e)),{method:"DELETE",headers:{"Content-Type":"application/json"}})}}]);