"use strict";exports.id=240,exports.ids=[240],exports.modules={86760:(e,r,o)=>{o.d(r,{Z:()=>v});var l=o(16689),t=o.n(l),n=o(59003),a=o.n(n),s=o(82142);function c(e){return["small","middle","large"].includes(e)}function i(e){return!!e&&"number"==typeof e&&!Number.isNaN(e)}var u=o(76393),d=o(60299);let p=t().createContext({latestIndex:0}),g=p.Provider,m=e=>{let{className:r,index:o,children:t,split:n,style:a}=e,{latestIndex:s}=l.useContext(p);return null==t?null:l.createElement(l.Fragment,null,l.createElement("div",{className:r,style:a},t),o<s&&n&&l.createElement("span",{className:`${r}-split`},n))};var b=o(91394),f=function(e,r){var o={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&0>r.indexOf(l)&&(o[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var t=0,l=Object.getOwnPropertySymbols(e);t<l.length;t++)0>r.indexOf(l[t])&&Object.prototype.propertyIsEnumerable.call(e,l[t])&&(o[l[t]]=e[l[t]]);return o};let y=l.forwardRef((e,r)=>{var o;let{getPrefixCls:t,direction:n,size:d,className:p,style:y,classNames:v,styles:C}=(0,u.dj)("space"),{size:$=null!=d?d:"small",align:h,className:O,rootClassName:k,children:x,direction:S="horizontal",prefixCls:j,split:w,style:E,wrap:N=!1,classNames:P,styles:T}=e,I=f(e,["size","align","className","rootClassName","children","direction","prefixCls","split","style","wrap","classNames","styles"]),[B,z]=Array.isArray($)?$:[$,$],Z=c(z),H=c(B),F=i(z),M=i(B),R=(0,s.Z)(x,{keepEmpty:!0}),A=void 0===h&&"horizontal"===S?"center":h,L=t("space",j),[q,W,D]=(0,b.Z)(L),G=a()(L,p,W,`${L}-${S}`,{[`${L}-rtl`]:"rtl"===n,[`${L}-align-${A}`]:A,[`${L}-gap-row-${z}`]:Z,[`${L}-gap-col-${B}`]:H},O,k,D),_=a()(`${L}-item`,null!==(o=null==P?void 0:P.item)&&void 0!==o?o:v.item),Q=0,U=R.map((e,r)=>{var o;null!=e&&(Q=r);let t=(null==e?void 0:e.key)||`${_}-${r}`;return l.createElement(m,{className:_,key:t,index:r,split:w,style:null!==(o=null==T?void 0:T.item)&&void 0!==o?o:C.item},e)}),X=l.useMemo(()=>({latestIndex:Q}),[Q]);if(0===R.length)return null;let J={};return N&&(J.flexWrap="wrap"),!H&&M&&(J.columnGap=B),!Z&&F&&(J.rowGap=z),q(l.createElement("div",Object.assign({ref:r,className:G,style:Object.assign(Object.assign(Object.assign({},J),y),E)},I),l.createElement(g,{value:X},U)))});y.Compact=d.ZP;let v=y},28802:(e,r,o)=>{o.d(r,{Z:()=>P});var l=o(16689),t=o(59003),n=o.n(t),a=o(78225),s=o(29049),c=o(28426),i=o(82748),u=o(28317),d=o(76393),p=o(52727),g=o(79712),m=o(92929),b=o(10045),f=o(83505);let y=e=>{let{paddingXXS:r,lineWidth:o,tagPaddingHorizontal:l,componentCls:t,calc:n}=e,a=n(l).sub(o).equal(),s=n(r).sub(o).equal();return{[t]:Object.assign(Object.assign({},(0,m.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:a,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,p.unit)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${t}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${t}-close-icon`]:{marginInlineStart:s,fontSize:e.tagIconSize,color:e.colorTextDescription,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${t}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${t}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:a}}),[`${t}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}},v=e=>{let{lineWidth:r,fontSizeIcon:o,calc:l}=e,t=e.fontSizeSM;return(0,b.mergeToken)(e,{tagFontSize:t,tagLineHeight:(0,p.unit)(l(e.lineHeightSM).mul(t).equal()),tagIconSize:l(o).sub(l(r).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},C=e=>({defaultBg:new g.FastColor(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText}),$=(0,f.I$)("Tag",e=>y(v(e)),C);var h=function(e,r){var o={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&0>r.indexOf(l)&&(o[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var t=0,l=Object.getOwnPropertySymbols(e);t<l.length;t++)0>r.indexOf(l[t])&&Object.prototype.propertyIsEnumerable.call(e,l[t])&&(o[l[t]]=e[l[t]]);return o};let O=l.forwardRef((e,r)=>{let{prefixCls:o,style:t,className:a,checked:s,onChange:c,onClick:i}=e,u=h(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:p,tag:g}=l.useContext(d.E_),m=p("tag",o),[b,f,y]=$(m),v=n()(m,`${m}-checkable`,{[`${m}-checkable-checked`]:s},null==g?void 0:g.className,a,f,y);return b(l.createElement("span",Object.assign({},u,{ref:r,style:Object.assign(Object.assign({},t),null==g?void 0:g.style),className:v,onClick:e=>{null==c||c(!s),null==i||i(e)}})))});var k=o(19006);let x=e=>(0,k.Z)(e,(r,o)=>{let{textColor:l,lightBorderColor:t,lightColor:n,darkColor:a}=o;return{[`${e.componentCls}${e.componentCls}-${r}`]:{color:l,background:n,borderColor:t,"&-inverse":{color:e.colorTextLightSolid,background:a,borderColor:a},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}}),S=(0,f.bk)(["Tag","preset"],e=>x(v(e)),C),j=(e,r,o)=>{let l=function(e){return"string"!=typeof e?e:e.charAt(0).toUpperCase()+e.slice(1)}(o);return{[`${e.componentCls}${e.componentCls}-${r}`]:{color:e[`color${o}`],background:e[`color${l}Bg`],borderColor:e[`color${l}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}},w=(0,f.bk)(["Tag","status"],e=>{let r=v(e);return[j(r,"success","Success"),j(r,"processing","Info"),j(r,"error","Error"),j(r,"warning","Warning")]},C);var E=function(e,r){var o={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&0>r.indexOf(l)&&(o[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var t=0,l=Object.getOwnPropertySymbols(e);t<l.length;t++)0>r.indexOf(l[t])&&Object.prototype.propertyIsEnumerable.call(e,l[t])&&(o[l[t]]=e[l[t]]);return o};let N=l.forwardRef((e,r)=>{let{prefixCls:o,className:t,rootClassName:p,style:g,children:m,icon:b,color:f,onClose:y,bordered:v=!0,visible:C}=e,h=E(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:O,direction:k,tag:x}=l.useContext(d.E_),[j,N]=l.useState(!0),P=(0,a.Z)(h,["closeIcon","closable"]);l.useEffect(()=>{void 0!==C&&N(C)},[C]);let T=(0,s.o2)(f),I=(0,s.yT)(f),B=T||I,z=Object.assign(Object.assign({backgroundColor:f&&!B?f:void 0},null==x?void 0:x.style),g),Z=O("tag",o),[H,F,M]=$(Z),R=n()(Z,null==x?void 0:x.className,{[`${Z}-${f}`]:B,[`${Z}-has-color`]:f&&!B,[`${Z}-hidden`]:!j,[`${Z}-rtl`]:"rtl"===k,[`${Z}-borderless`]:!v},t,p,F,M),A=e=>{e.stopPropagation(),null==y||y(e),e.defaultPrevented||N(!1)},[,L]=(0,c.Z)((0,c.w)(e),(0,c.w)(x),{closable:!1,closeIconRender:e=>{let r=l.createElement("span",{className:`${Z}-close-icon`,onClick:A},e);return(0,i.wm)(e,r,e=>({onClick:r=>{var o;null===(o=null==e?void 0:e.onClick)||void 0===o||o.call(e,r),A(r)},className:n()(null==e?void 0:e.className,`${Z}-close-icon`)}))}}),q="function"==typeof h.onClick||m&&"a"===m.type,W=b||null,D=W?l.createElement(l.Fragment,null,W,m&&l.createElement("span",null,m)):m,G=l.createElement("span",Object.assign({},P,{ref:r,className:R,style:z}),D,L,T&&l.createElement(S,{key:"preset",prefixCls:Z}),I&&l.createElement(w,{key:"status",prefixCls:Z}));return H(q?l.createElement(u.Z,{component:"Tag"},G):G)});N.CheckableTag=O;let P=N}};