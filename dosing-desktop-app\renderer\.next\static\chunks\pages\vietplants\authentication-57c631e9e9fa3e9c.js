(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[267],{6128:function(e,l,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/vietplants/authentication",function(){return n(274)}])},274:function(e,l,n){"use strict";n.r(l),n.d(l,{ForgotPasswordPage:function(){return y},LoginPage:function(){return m}});var t=n(4246),i=n(5475),r=n(3992),o=n(4478),s=n(8218),a=n(7740),d=n(6316),c=n(3779),u=n(2882),p=n(624),h=n(5942),g=n(7378),x=n(4717),f=n(1555);let m=()=>{let e=(0,x.Z)(e=>e.languageData),l=(0,h.Z)(e=>e.setEmail),[n,c]=(0,g.useState)([]);(0,g.useEffect)(()=>{!async function(){try{var e;let l=await (0,p.v8)();c(null==l?void 0:null===(e=l.responseData)||void 0===e?void 0:e.data)}catch(e){i.ZP.error("C\xf3 lỗi xảy ra khi lấy th\xf4ng tin người d\xf9ng! Vui l\xf2ng thử lại sau.")}}()},[]);let[m,y]=(0,g.useState)([]);(0,g.useEffect)(()=>{console.log("listOfAllValidUsers: ",n),n&&(null==n?void 0:n.length)!==0&&y(n.map(e=>({label:(0,t.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:4,borderBottom:"1px solid #ddd"},children:[(0,t.jsx)("p",{style:{fontWeight:"bold",margin:0},children:(null==e?void 0:e.last_name)+" "+(null==e?void 0:e.first_name)}),(0,t.jsx)("p",{style:{color:"rgb(100,100,100)",margin:0},children:null==e?void 0:e.email})]}),value:null==e?void 0:e.email})))},[n]);let v=async n=>{try{await (0,p.x4)({...n}).then(e=>{var n,t,i,r;localStorage.setItem("token",JSON.stringify(null==e?void 0:null===(n=e.responseData)||void 0===n?void 0:n.result)),l(null==e?void 0:null===(r=e.responseData)||void 0===r?void 0:null===(i=r.result)||void 0===i?void 0:null===(t=i.user)||void 0===t?void 0:t.email)})}catch(l){console.error("Login error:",l),i.ZP.error(e["common.login.error"])}};return(0,t.jsxs)(r.Z,{justify:"center",align:"top",className:"center-top-screen flex flex-col",children:[(0,t.jsxs)(o.default,{style:{width:"400px",padding:"20px",borderRadius:"10px",backgroundColor:"#fff",marginBottom:"20px"},onFinish:v,children:[(0,t.jsxs)("div",{style:{display:"flex",flexDirection:"column",alignItems:"center"},children:[(0,t.jsx)(s.Z,{src:"/images/Vietplants-logo.png",width:100,preview:!1}),(0,t.jsx)("p",{style:{marginTop:"20px",fontSize:"24px",fontWeight:"bold"},children:"Đăng nhập"})]}),(0,t.jsx)(u.Z,{name:"usr",children:(0,t.jsx)(a.Z,{options:m,size:"large",placeholder:"Chọn hoặc nhập t\xe0i khoản",filterOption:(e,l)=>null==l?void 0:l.value.toLowerCase().includes(e.toLowerCase())})}),(0,t.jsx)(u.Z,{name:"pwd",children:(0,t.jsx)(f.Z,{placeholder:"Mật khẩu",size:"large",type:"password"})}),(0,t.jsx)(d.ZP,{type:"primary",htmlType:"submit",style:{width:"100%"},size:"large",children:"Login"})]}),(0,t.jsx)(d.ZP,{disabled:!0,title:"Tạm thời chưa hỗ trợ lấy lại mật khẩu",type:"link",href:"/user/forgot-password",children:"Forgot password?"}),(0,t.jsxs)("p",{style:{marginTop:"30px",textAlign:"center",color:"rgb(100,100,100)"},children:[" ","\xa92025 Powered by VIIS"," "]})]})};l.default=m;let y=()=>(0,t.jsxs)(r.Z,{justify:"center",align:"top",className:"center-top-screen flex flex-col",children:[(0,t.jsxs)(o.default,{style:{width:"400px",padding:"20px",borderRadius:"10px",backgroundColor:"#fff",marginBottom:"20px"},children:[(0,t.jsx)("p",{style:{fontSize:"20px",fontWeight:"bold",marginBottom:"40px"},children:"Qu\xean mật khẩu"}),(0,t.jsx)(u.Z,{name:"username",children:(0,t.jsx)(c.Z,{placeholder:"Email người d\xf9ng",size:"large"})}),(0,t.jsx)(d.ZP,{type:"primary",htmlType:"submit",style:{width:"100%"},size:"large",children:"Lấy lại mật khẩu"})]}),(0,t.jsx)("a",{href:"/user/login",children:"Quay về trang đăng nhập"}),(0,t.jsxs)("p",{style:{marginTop:"30px",textAlign:"center",color:"rgb(100,100,100)"},children:[" ","\xa9 2025 Powered by VIIS"," "]})]})}},function(e){e.O(0,[467,899,478,979,888,774,179],function(){return e(e.s=6128)}),_N_E=e.O()}]);