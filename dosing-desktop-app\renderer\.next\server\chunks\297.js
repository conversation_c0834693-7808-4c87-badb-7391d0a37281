"use strict";exports.id=297,exports.ids=[297],exports.modules={48490:(e,t,n)=>{n.a(e,async(e,i)=>{try{n.d(t,{Z:()=>p});var r=n(20997),a=n(65890),o=n(75504),l=n(16689),s=n(60200),d=n(82834),c=n(49281),u=n(48764),f=n(7965),g=n(52857),m=e([s,u,a]);[s,u,a]=m.then?(await m)():m;let h={controlItem:{width:"100%",display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"space-between",gap:8,border:"1px solid rgb(230,230,230)",padding:8,borderRadius:8,backgroundColor:"#fff"}},p=({functionItem:e,setLatestDigitValue:t})=>{let{deviceData:n,deviceId:i}=(0,s.Z)(),{data:m,loading:p,run:x}=(0,d.Z)(),[v,y]=(0,l.useState)(!1),{run:b,loading:w}=(0,c.Z)(),{handleMessage:C,client:S}=(0,u.T)(),[j,N]=(0,l.useState)(0);function Z(){n.name&&e.identifier&&(console.log("Start to get new data"),x({deviceId:n?.name,keys:[e.identifier]}))}(0,l.useEffect)(()=>{Z()},[n?.name,e.identifier]),(0,l.useEffect)(()=>{if(!n?.name||!e.identifier)return;let i=m?.data?.[e?.identifier]||[];if(console.log("latestDataWithFunctionKey",i),i.length>0){let e=i[i.length-1];e&&(N(Number(e?.value)),t&&t(Number(e.value)))}m?.data&&y(!0)},[m,n?.name,e.identifier]),(0,l.useEffect)(()=>{v&&S.on("message",(n,r)=>{if(n===(0,f.v)(i)){console.log(`DIGIT CONTROL: Received MQTT message on topic ${n}:`,r.toString());try{let n=JSON.parse(r.toString());if(Array.isArray(n)){let i=n.filter(t=>t.key===e.identifier);if(i.length>0){let e=i[i.length-1];N(Number(e?.value)),t&&t(Number(e.value))}}}catch(e){console.error("MQTT message error:",e)}}})},[C,n?.name,e.identifier]);let k=async t=>{let i=n?.name;i&&e.identifier&&(await b({device_id_thingsboard:i,method:"set_state",params:{[e.identifier]:t}}).then(()=>{N(0)}),Z())};return r.jsx("div",{style:h.controlItem,children:r.jsx(a.default,{layout:"vertical",style:{width:"100%"},children:(0,r.jsxs)("div",{style:{width:"100%",display:"flex",flexDirection:"row",justifyContent:"space-between",flexWrap:"wrap",gap:"8px",alignItems:"end"},children:[r.jsx(a.default.Item,{style:{width:"calc(100% - 70px)",marginBottom:0},name:e.name,label:r.jsx("p",{style:{color:"rgb(100,100,100)",margin:0},children:e.label}),required:!0,children:r.jsx(g.Z,{style:{width:"100%"},onChange:e=>N(e),placeholder:j?.toString()})}),r.jsx(a.default.Item,{style:{marginBottom:0},children:r.jsx(o.ZP,{type:"primary",style:{width:"60px"},onClick:()=>k(j),children:"OK"})})]})})})};i()}catch(e){i(e)}})},27529:(e,t,n)=>{n.a(e,async(e,i)=>{try{n.d(t,{Z:()=>x});var r=n(20997),a=n(16689),o=n(6013),l=n(345),s=n(36246),d=n(46463),c=n(82834),u=n(60200),f=n(49281),g=n(48764),m=n(7965),h=e([o,u,g]);[o,u,g]=h.then?(await h)():h;let p={controlItem:{width:"100%",display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"space-between",gap:8,border:"1px solid rgb(230,230,230)",padding:8,borderRadius:8,backgroundColor:"#fff"}},x=({functionItem:e,readonly:t,setResponseStatusOfOnOffControl:n})=>{let{languageData:i}=(0,o.Z)(),{deviceData:h,deviceId:x}=(0,u.Z)(),{data:v,loading:y,run:b}=(0,c.Z)(),[w,C]=(0,a.useState)(!1),{run:S,loading:j}=(0,f.Z)(),{handleMessage:N,client:Z}=(0,g.T)(),[k,_]=(0,a.useState)(!1),E=e=>"true"===String(e);function I(){h.name&&e.identifier&&(console.log("Start to get new data"),b({deviceId:h?.name,keys:[e.identifier]}))}(0,a.useEffect)(()=>{I()},[h?.name,e.identifier]),(0,a.useEffect)(()=>{if(!h?.name||!e.identifier)return;let t=v?.data?.[e?.identifier]||[];if(console.log("latestDataWithFunctionKey",t),t.length>0){let e=t[t.length-1];e&&_(E(e?.value))}v?.data&&C(!0)},[v,h?.name,e.identifier]),(0,a.useEffect)(()=>{w&&Z.on("message",(t,n)=>{if(t===(0,m.v)(x)){console.log(`ON OFF CONTROL: Received MQTT message on topic ${t}:`,n.toString());try{let t=JSON.parse(n.toString());if(Array.isArray(t)){let n=t.filter(t=>t.key===e.identifier);if(n.length>0){let e=n[n.length-1];e&&_(E(e?.value))}}}catch(e){console.error("MQTT message error:",e)}}})},[w,N,h?.name,e.identifier]);let T=async t=>{let n=h?.name;n&&e.identifier&&(await S({device_id_thingsboard:n,method:"set_state",params:{[e.identifier]:t}}),I())};return(0,a.useEffect)(()=>{n&&n(k)},[k]),(0,r.jsxs)("div",{style:p.controlItem,children:[(0,r.jsxs)("div",{style:{display:"flex",flexDirection:"row",alignItems:"center",gap:8},children:[e.icon_url?r.jsx("img",{height:"24px",src:(0,l.rH)("api/v2/file/download?file_url="+e.icon_url),onError:()=>r.jsx(s.Z,{})}):r.jsx(s.Z,{}),r.jsx("p",{style:{margin:0,color:"rgb(100,100,100)"},children:e.label})]}),r.jsx(d.Z,{disabled:t,value:k,onChange:T,checked:k,loading:j||y,checkedChildren:e.data_on_text?e.data_on_text:i["common.control.switch.on"],unCheckedChildren:e.data_off_text?e.data_off_text:i["common.control.switch.off"],style:{minWidth:"60px"}})]})};i()}catch(e){i(e)}})},52857:(e,t,n)=>{n.d(t,{Z:()=>d});var i=n(20997),r=n(16689),a=n.n(r),o=n(91326),l=n(47059),s=n.n(l);n(73198);let d=a().forwardRef((e,t)=>{let[n,a]=(0,r.useState)(""),[l,d]=(0,r.useState)(!1),c=(0,r.useRef)(null),u=(0,r.useRef)(null),f=(0,r.useRef)(null);(0,r.useEffect)(()=>{t&&(t.current=u.current)},[t]),(0,r.useEffect)(()=>{void 0!==e.value&&null!==e.value&&String(e.value)!==n&&a(String(e.value))},[e.value]);let g=t=>{console.log("input: ",t);let n=Number(t),i=""!==t&&!isNaN(n);t&&i?(a(t),e.onChange&&e.onChange(n)):(a(void 0),e.onChange&&e.onChange?.(void 0))},m=()=>{d(!0),setTimeout(()=>{c.current?.setInput(n)},100)};return(0,r.useEffect)(()=>{let e=e=>{!u.current||u.current.inputElement?.contains(e.target)||f.current?.contains(e.target)||d(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),(0,i.jsxs)(i.Fragment,{children:[i.jsx(o.Z,{...e,ref:u,value:""===n||isNaN(Number(n))?void 0:Number(n),onChange:t=>{let n=Number(t);""!==t&&!isNaN(n)&&(a(("string"==typeof t&&t.includes("."),n.toString())),e.onChange&&e.onChange(n))},onFocus:m,onBlur:()=>{let t=n?.replace(/[^0-9.]/g,""),i=Number(t);!t||isNaN(i)?(a(void 0),e.onChange?.(void 0)):(a(t),e.onChange?.(i))},onClick:m,precision:void 0!==e.precision?e.precision:2,step:void 0!==e.step?e.step:.01}),l&&(0,i.jsxs)("div",{ref:f,className:"rsk-container",style:{position:"fixed",bottom:0,left:0,width:"100%",background:"rgba(255,255,255,0.2)",boxShadow:"0 -2px 8px rgba(0,0,0,0.15)",zIndex:1e3},children:[i.jsx("div",{style:{padding:"8px 16px",borderBottom:"1px solid #e8e8e8",fontSize:"16px",fontWeight:"bold",background:"#fafafa",whiteSpace:"nowrap",overflowX:"auto"},children:n||"\xa0"}),i.jsx(s(),{ref:c,onChange:g,onKeyPress:e=>{if("{bksp}"===e){g(n?.slice(0,-1)||"");return}if("{enter}"===e){d(!1),u.current?.blur();return}"."===e&&(n||"").includes(".")||g((n||"")+e)},layout:{default:["1 2 3","4 5 6","7 8 9",". 0 {bksp}","{enter}"]},display:{"{bksp}":"⌫","{enter}":"⏎"}})]})]})})},49281:(e,t,n)=>{n.d(t,{Z:()=>a});var i=n(185),r=n(21158);let a=({onSuccess:e}={})=>(0,r.Q)(i.A2,{onError(e,t){},onSuccess(t,n){e?.(t)},manual:!0})},82834:(e,t,n)=>{n.d(t,{Z:()=>a});var i=n(185),r=n(21158);let a=({onSuccess:e}={})=>(0,r.Q)(i.$U,{onError(e,t){},onSuccess(t,n){e?.(t)}})}};