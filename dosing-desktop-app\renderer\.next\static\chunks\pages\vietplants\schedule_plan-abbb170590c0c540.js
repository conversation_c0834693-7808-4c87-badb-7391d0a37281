(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[211],{6054:function(e,t,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/vietplants/schedule_plan",function(){return n(5255)}])},6111:function(e,t,n){"use strict";n.d(t,{Z:function(){return o}});var l=n(5773),i=n(7378),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"}},{tag:"path",attrs:{d:"M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"}}]},name:"plus",theme:"outlined"},r=n(3359),o=i.forwardRef(function(e,t){return i.createElement(r.Z,(0,l.Z)({},e,{ref:t,icon:a}))})},5255:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return es}});var l=n(4246),i=n(7378),a=n(2096),r=n(9739),o=n(1629),s=n(3992),d=n(1671),c=n(5),h=n.n(c),u=n(8539),g=n(7349),m=n(5334),p=n(4547),x=n(4645);let f=e=>{let{componentCls:t,sizePaddingEdgeHorizontal:n,colorSplit:l,lineWidth:i,textPaddingInline:a,orientationMargin:r,verticalMarginInline:o}=e;return{[t]:Object.assign(Object.assign({},(0,m.Wf)(e)),{borderBlockStart:"".concat((0,g.bf)(i)," solid ").concat(l),"&-vertical":{position:"relative",top:"-0.06em",display:"inline-block",height:"0.9em",marginInline:o,marginBlock:0,verticalAlign:"middle",borderTop:0,borderInlineStart:"".concat((0,g.bf)(i)," solid ").concat(l)},"&-horizontal":{display:"flex",clear:"both",width:"100%",minWidth:"100%",margin:"".concat((0,g.bf)(e.dividerHorizontalGutterMargin)," 0")},["&-horizontal".concat(t,"-with-text")]:{display:"flex",alignItems:"center",margin:"".concat((0,g.bf)(e.dividerHorizontalWithTextGutterMargin)," 0"),color:e.colorTextHeading,fontWeight:500,fontSize:e.fontSizeLG,whiteSpace:"nowrap",textAlign:"center",borderBlockStart:"0 ".concat(l),"&::before, &::after":{position:"relative",width:"50%",borderBlockStart:"".concat((0,g.bf)(i)," solid transparent"),borderBlockStartColor:"inherit",borderBlockEnd:0,transform:"translateY(50%)",content:"''"}},["&-horizontal".concat(t,"-with-text-start")]:{"&::before":{width:"calc(".concat(r," * 100%)")},"&::after":{width:"calc(100% - ".concat(r," * 100%)")}},["&-horizontal".concat(t,"-with-text-end")]:{"&::before":{width:"calc(100% - ".concat(r," * 100%)")},"&::after":{width:"calc(".concat(r," * 100%)")}},["".concat(t,"-inner-text")]:{display:"inline-block",paddingBlock:0,paddingInline:a},"&-dashed":{background:"none",borderColor:l,borderStyle:"dashed",borderWidth:"".concat((0,g.bf)(i)," 0 0")},["&-horizontal".concat(t,"-with-text").concat(t,"-dashed")]:{"&::before, &::after":{borderStyle:"dashed none none"}},["&-vertical".concat(t,"-dashed")]:{borderInlineStartWidth:i,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},"&-dotted":{background:"none",borderColor:l,borderStyle:"dotted",borderWidth:"".concat((0,g.bf)(i)," 0 0")},["&-horizontal".concat(t,"-with-text").concat(t,"-dotted")]:{"&::before, &::after":{borderStyle:"dotted none none"}},["&-vertical".concat(t,"-dotted")]:{borderInlineStartWidth:i,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},["&-plain".concat(t,"-with-text")]:{color:e.colorText,fontWeight:"normal",fontSize:e.fontSize},["&-horizontal".concat(t,"-with-text-start").concat(t,"-no-default-orientation-margin-start")]:{"&::before":{width:0},"&::after":{width:"100%"},["".concat(t,"-inner-text")]:{paddingInlineStart:n}},["&-horizontal".concat(t,"-with-text-end").concat(t,"-no-default-orientation-margin-end")]:{"&::before":{width:"100%"},"&::after":{width:0},["".concat(t,"-inner-text")]:{paddingInlineEnd:n}}})}};var y=(0,p.I$)("Divider",e=>[f((0,x.IX)(e,{dividerHorizontalWithTextGutterMargin:e.margin,dividerHorizontalGutterMargin:e.marginLG,sizePaddingEdgeHorizontal:0}))],e=>({textPaddingInline:"1em",orientationMargin:.05,verticalMarginInline:e.marginXS}),{unitless:{orientationMargin:!0}}),b=function(e,t){var n={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&0>t.indexOf(l)&&(n[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,l=Object.getOwnPropertySymbols(e);i<l.length;i++)0>t.indexOf(l[i])&&Object.prototype.propertyIsEnumerable.call(e,l[i])&&(n[l[i]]=e[l[i]]);return n},j=e=>{let{getPrefixCls:t,direction:n,className:l,style:a}=(0,u.dj)("divider"),{prefixCls:r,type:o="horizontal",orientation:s="center",orientationMargin:d,className:c,rootClassName:g,children:m,dashed:p,variant:x="solid",plain:f,style:j}=e,v=b(e,["prefixCls","type","orientation","orientationMargin","className","rootClassName","children","dashed","variant","plain","style"]),w=t("divider",r),[_,Z,S]=y(w),k=!!m,C=i.useMemo(()=>"left"===s?"rtl"===n?"end":"start":"right"===s?"rtl"===n?"start":"end":s,[n,s]),I="start"===C&&null!=d,T="end"===C&&null!=d,D=h()(w,l,Z,S,"".concat(w,"-").concat(o),{["".concat(w,"-with-text")]:k,["".concat(w,"-with-text-").concat(C)]:k,["".concat(w,"-dashed")]:!!p,["".concat(w,"-").concat(x)]:"solid"!==x,["".concat(w,"-plain")]:!!f,["".concat(w,"-rtl")]:"rtl"===n,["".concat(w,"-no-default-orientation-margin-start")]:I,["".concat(w,"-no-default-orientation-margin-end")]:T},c,g),z=i.useMemo(()=>"number"==typeof d?d:/^\d+$/.test(d)?Number(d):d,[d]);return _(i.createElement("div",Object.assign({className:D,style:Object.assign(Object.assign({},a),j)},v,{role:"separator"}),m&&"vertical"!==o&&i.createElement("span",{className:"".concat(w,"-inner-text"),style:{marginInlineStart:I?z:void 0,marginInlineEnd:T?z:void 0}},m)))},v=n(6316),w=n(2094),_=n(7420),Z=n(4478),S=n(5475),k=n(213),C=n(4899),I=n(7693),T=n.n(I),D=n(9175),z=n(9731),O=n(6111),E=n(3563),P=n(3295),B=n(4582),H=n(5032),N=n(2754),M=n(2450),q=n(1555),W=n(22),R=e=>{var t,n;let{onClose:c,deviceId:h,schedulePlanId:u,start_date:g,end_date:m}=e,[p]=Z.default.useForm(),{functionListForControl:x}=(0,r.Z)(),{schedulePlans:f,setSchedulePlans:y,scheduleProgramTriggerImmediately:b}=(0,a.Z)(),[j,w]=(0,i.useState)(["0","1","2","3","4","5","6"]),[_,I]=(0,i.useState)([T()(g),T()(m)]),[D,z]=(0,i.useState)([]);(0,i.useEffect)(()=>{b&&z(b.enum_value.split(",").map(e=>({value:e.trim(),label:e.trim()})))},[b]);let O=async e=>{try{let i=Object.fromEntries(Object.entries(e.action||{}).map(e=>{let[t,n]=e;return"boolean"==typeof n?[t,String(n)]:"number"==typeof n||"string"==typeof n?[t,n]:[t,String(n)]})),a={name:e.name,start_time:e.start_time.format("HH:mm:ss"),end_time:e.start_time.add(e.time_running,"seconds").format("HH:mm:ss"),start_date:_[0].format("YYYY-MM-DD"),end_date:_[1].format("YYYY-MM-DD"),interval:e.interval.join(","),enable:1,schedule_plan_id:u,device_id:h,type:"",action:i};console.log("programToPush: ",a);let r=await (0,o.Jn)(a);if(null==r?void 0:r.statusOK){var t,n,l;S.ZP.success("Tạo chương tr\xecnh th\xe0nh c\xf4ng");let e=[...f];null===(l=e.find(e=>e.name===u))||void 0===l||l.schedules.push(null==r?void 0:null===(n=r.responseData)||void 0===n?void 0:null===(t=n.result)||void 0===t?void 0:t.data),y(e),p.resetFields(),c()}}catch(e){S.ZP.error("Vui l\xf2ng nhập đầy đủ th\xf4ng tin")}};return(0,l.jsxs)(Z.default,{layout:"vertical",form:p,style:{width:"100%"},children:[(0,l.jsxs)("div",{style:{zIndex:100,position:"fixed",bottom:24,right:24,display:"flex",justifyContent:"flex-end",gap:8,padding:8,background:"rgba(255, 255, 255, 0.5)",borderRadius:8,backdropFilter:"blur(5px)",border:"1px solid #ddd",boxShadow:"0px 0px 50px 2px rgba(0, 0, 0, 0.25)"},children:[(0,l.jsx)(v.ZP,{onClick:()=>c(),children:"Hủy"}),(0,l.jsx)(v.ZP,{type:"primary",onClick:()=>O(p.getFieldsValue()),children:"Lưu"})]}),(0,l.jsx)(Z.default.Item,{name:"interval",label:"\xc1p dụng cho c\xe1c thứ",rules:[{required:!0}],children:(0,l.jsx)(P.Z.Group,{options:[{label:"Chủ Nhật",value:"0"},{label:"Thứ 2",value:"1"},{label:"Thứ 3",value:"2"},{label:"Thứ 4",value:"3"},{label:"Thứ 5",value:"4"},{label:"Thứ 6",value:"5"},{label:"Thứ 7",value:"6"}],value:j,onChange:e=>w(e)})}),(0,l.jsx)(s.Z,{gutter:[16,16],children:(0,l.jsx)(d.Z,{span:24,children:(0,l.jsx)(Z.default.Item,{name:"name",label:"T\xean chương tr\xecnh",rules:[{required:!0}],layout:"vertical",children:(0,l.jsx)(q.Z,{style:{width:"100%"}})})})}),(0,l.jsxs)(s.Z,{gutter:[16,16],children:[(0,l.jsx)(d.Z,{span:12,children:(0,l.jsx)(Z.default.Item,{name:"start_time",label:"Thời gian bắt đầu",rules:[{required:!0}],layout:"vertical",children:(0,l.jsx)(B.Z,{style:{width:"100%"}})})}),(0,l.jsx)(d.Z,{span:12,children:(0,l.jsx)(Z.default.Item,{name:"time_running",label:"Thời gian thực hiện (Gi\xe2y)",rules:[{required:!0}],layout:"vertical",children:(0,l.jsx)(W.Z,{style:{width:"100%"}})})})]}),(0,l.jsx)(s.Z,{gutter:[16,16],children:(0,l.jsx)(d.Z,{span:24,children:(0,l.jsx)(Z.default.Item,{rules:[{required:!0}],label:"Ng\xe0y thực hiện",children:(0,l.jsx)(k.default.RangePicker,{style:{width:"100%"},onChange:e=>I(e),disabledDate:e=>{let t=T()().startOf("day"),n=T()(g),l=t.isBefore(n)?n:t,i=T()(m);return e&&(e<l||e>i)}},"date_range_picker")})})}),(0,l.jsx)(s.Z,{gutter:[16,16],children:(0,l.jsx)(d.Z,{span:12,children:(0,l.jsx)(Z.default.Item,{name:["action","env_enum"],rules:[{required:!0}],label:"M\xe3 m\xf4i trường",children:(0,l.jsx)(C.default,{placeholder:"Chọn m\xe3 m\xf4i trường",style:{width:"100%"},options:D})})})}),(0,l.jsx)(d.Z,{span:24,style:{marginTop:32},children:null===(n=x.find(e=>"tb1"===e.identifier))||void 0===n?void 0:null===(t=n.children)||void 0===t?void 0:t.map(e=>{var t;return 0===e.children.length?null:(0,l.jsxs)(s.Z,{style:{marginBottom:32},children:[(0,l.jsx)("p",{style:{margin:0,fontSize:16,fontWeight:"bold"},children:e.label}),(0,l.jsx)(d.Z,{span:24,style:{marginTop:8},children:null==e?void 0:null===(t=e.children)||void 0===t?void 0:t.map(e=>(0,l.jsx)(s.Z,{gutter:[16,16],style:{borderTop:"1px solid #ddd"},children:(0,l.jsx)(d.Z,{span:24,children:(0,l.jsxs)(Z.default.Item,{style:{marginBottom:0},name:["action",e.identifier],initialValue:"Bool"!==e.data_type&&0,layout:"horizontal",labelCol:{span:12,style:{textAlign:"left"}},wrapperCol:{span:12,style:{textAlign:"right"}},label:(0,l.jsxs)("div",{style:{display:"flex",flexDirection:"row",alignItems:"center"},children:[e.icon_url?(0,l.jsx)("img",{height:"24px",src:(0,N.rH)("api/v2/file/download?file_url="+e.icon_url),onError:()=>(0,l.jsx)(M.Z,{})}):(0,l.jsx)(M.Z,{}),(0,l.jsx)("p",{style:{margin:0,marginLeft:8},children:e.label})]}),children:["Bool"===e.data_type&&(0,l.jsx)(H.Z,{style:{width:40}}),"Value"===e.data_type&&(0,l.jsx)(W.Z,{style:{width:200,marginTop:4,marginBottom:4}})]})})},e.identifier))})]},e.label)})}),(0,l.jsx)("div",{style:{height:80}})]})},Y=n(1216),F=n(7767),V=e=>{let{plan_id:t}=e,[n,r]=(0,i.useState)(!1),{schedulePlans:s,setSchedulePlans:d}=(0,a.Z)(),c=async()=>{let e=await (0,o.uI)(t);(null==e?void 0:e.statusOK)&&(d(s.filter(e=>e.name!==t)),S.ZP.success("X\xf3a kế hoạch th\xe0nh c\xf4ng")),r(!1)};return(0,l.jsxs)("div",{children:[(0,l.jsx)(v.ZP,{icon:(0,l.jsx)(F.Z,{}),danger:!0,onClick:()=>r(!0),children:"X\xf3a kế hoạch"}),(0,l.jsx)(Y.Z,{open:n,onOk:c,onCancel:()=>r(!1),okButtonProps:{type:"primary",title:"X\xf3a",danger:!0},okText:"X\xf3a",cancelButtonProps:{type:"default",title:"Hủy"},cancelText:"Hủy",children:(0,l.jsx)("p",{style:{textAlign:"center",fontWeight:"bold",fontSize:"14px"},children:"Bạn c\xf3 chắc chắn muốn x\xf3a kế hoạch n\xe0y?"})})]})},G=e=>{var t,n,r;let{plan:c,onClose:h}=e,[u]=Z.default.useForm(),{schedulePlans:g,setSchedulePlans:m}=(0,a.Z)(),[p,x]=(0,i.useState)(!0),[f,y]=(0,i.useState)("action_time"),b=(e,t)=>{var n,l,i,a;"action_time"===t?null==c||null===(n=c.schedules)||void 0===n||n.sort((t,n)=>{let l=new Date().toISOString().split("T")[0],i=new Date("".concat(l,"T").concat(t.start_time)),a=new Date("".concat(l,"T").concat(n.start_time));return e?i.getTime()-a.getTime():a.getTime()-i.getTime()}):"program_name"===t?null==c||null===(l=c.schedules)||void 0===l||l.sort((t,n)=>e?t.name.localeCompare(n.name):n.name.localeCompare(t.name)):"creation"===t?null==c||null===(i=c.schedules)||void 0===i||i.sort((t,n)=>e?T()(t.creation).isBefore(T()(n.creation))?-1:1:T()(t.creation).isBefore(T()(n.creation))?1:-1):"modified"===t&&(null==c||null===(a=c.schedules)||void 0===a||a.sort((t,n)=>e?T()(t.modified).isBefore(T()(n.modified))?-1:1:T()(t.modified).isBefore(T()(n.modified))?1:-1))};(0,i.useEffect)(()=>{b(p,f)},[p,f]);let _=async e=>{let t={name:c.name,label:e.label,device_id:c.device_id,start_date:e.start_date,end_date:e.end_date,enable:c.enable?1:0},n=await (0,o.YL)(t);(null==n?void 0:n.statusOK)&&(m(g.map(n=>n.name===t.name?{...n,label:e.label,start_date:e.start_date,end_date:e.end_date}:n)),S.ZP.success("Cập nhật kế hoạch th\xe0nh c\xf4ng"),u.resetFields(),console.log(e),h())},[I,P]=(0,i.useState)(!1);return(0,l.jsxs)(Z.default,{form:u,style:{width:"100%"},children:[(0,l.jsxs)("div",{style:{zIndex:100,position:"fixed",bottom:24,right:24,display:"flex",justifyContent:"flex-end",gap:8,padding:8,background:"rgba(255, 255, 255, 0.5)",borderRadius:8,backdropFilter:"blur(5px)",border:"1px solid #ddd",boxShadow:"0px 0px 50px 2px rgba(0, 0, 0, 0.25)"},children:[(0,l.jsx)(v.ZP,{onClick:()=>h(),children:"Hủy"}),(0,l.jsx)(v.ZP,{type:"primary",onClick:()=>_(u.getFieldsValue()),children:"Lưu"})]}),(0,l.jsx)("div",{style:{display:"flex",flexDirection:"row",justifyContent:"flex-end",gap:8},children:(0,l.jsx)(V,{plan_id:c.name})}),(0,l.jsx)(s.Z,{gutter:[16,16],style:{marginBottom:24},children:(0,l.jsx)(d.Z,{span:24,children:(0,l.jsx)(Z.default.Item,{name:"label",label:"T\xean kế hoạch",rules:[{required:!0}],layout:"vertical",children:(0,l.jsx)(q.Z,{required:!0,defaultValue:c.label,style:{width:"100%"}})})})}),(0,l.jsxs)(s.Z,{gutter:[16,16],style:{marginBottom:32},children:[(0,l.jsx)(d.Z,{span:12,children:(0,l.jsx)(Z.default.Item,{name:"start_date",label:"Ng\xe0y bắt đầu",initialValue:T()(c.start_date),rules:[{required:!0}],layout:"vertical",children:(0,l.jsx)(k.default,{style:{width:"100%"}})})}),(0,l.jsx)(d.Z,{span:12,children:(0,l.jsx)(Z.default.Item,{name:"end_date",label:"Ng\xe0y kết th\xfac",initialValue:T()(c.end_date),rules:[{required:!0}],layout:"vertical",children:(0,l.jsx)(k.default,{style:{width:"100%"}})})})]}),(0,l.jsx)(j,{}),(0,l.jsxs)("div",{style:{display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"space-between",marginBottom:8},children:[(0,l.jsx)("p",{style:{fontSize:18,fontWeight:"bold",margin:0},children:"Danh s\xe1ch chương tr\xecnh"}),(0,l.jsxs)("div",{style:{display:"flex",flexDirection:"row",gap:8},children:[(0,l.jsx)(v.ZP,{type:"default",onClick:()=>x(e=>!e),icon:p?(0,l.jsx)(D.Z,{}):(0,l.jsx)(z.Z,{})}),(0,l.jsx)(C.default,{style:{width:"200px"},defaultValue:f,options:[{label:"Thời điểm thực hiện",value:"action_time"},{label:"T\xean chương tr\xecnh",value:"program_name"},{label:"Thời gian tạo",value:"creation"},{label:"Thời gian sửa",value:"modified"}],onChange:e=>{y(e)}})]})]}),(null==c?void 0:null===(t=c.schedules)||void 0===t?void 0:t.length)===0?(0,l.jsx)(l.Fragment,{}):(0,l.jsx)(v.ZP,{type:"link",icon:(0,l.jsx)(O.Z,{}),style:{borderRadius:8,marginBottom:8,padding:0},onClick:()=>{P(!0)},children:"Th\xeam chương tr\xecnh"}),(0,l.jsx)(s.Z,{gutter:[16,16],children:(null==c?void 0:null===(n=c.schedules)||void 0===n?void 0:n.length)===0?(0,l.jsxs)("div",{style:{width:"100%",display:"flex",flexDirection:"column",alignItems:"center",gap:16},children:[(0,l.jsx)("p",{style:{padding:8,color:"gray",margin:0},children:"*Chưa c\xf3 chương tr\xecnh n\xe0o được tạo"}),(0,l.jsx)(v.ZP,{type:"link",icon:(0,l.jsx)(O.Z,{}),onClick:()=>{P(!0)},children:"Th\xeam chương tr\xecnh"})]}):null==c?void 0:null===(r=c.schedules)||void 0===r?void 0:r.map(e=>(0,l.jsx)(d.Z,{span:24,children:(0,l.jsx)(E.Z,{program:e,start_date_of_plan:c.start_date,end_date_of_plan:c.end_date})}))}),(0,l.jsx)(w.Z,{title:"Th\xeam chương tr\xecnh",open:I,onClose:()=>{P(!1)},width:"70%",children:(0,l.jsx)(R,{onClose:()=>{P(!1)},deviceId:c.device_id,schedulePlanId:c.name,start_date:c.start_date,end_date:c.end_date})}),(0,l.jsx)("div",{style:{width:"100%",height:80}})]},c.name)},L=e=>{let{plan:t,setPlanActivated:n}=e,{schedulePlans:i,setSchedulePlans:r}=(0,a.Z)(),s=async e=>{let l={name:t.name,label:t.label,device_id:t.device_id,start_date:t.start_date,end_date:t.end_date,enable:e?1:0},a=await (0,o.YL)(l);(null==a?void 0:a.statusOK)&&(r(i.map(e=>e.name===l.name?{...e,enable:l.enable}:e)),S.ZP.success("".concat(e?"K\xedch hoạt":"Tắt"," kế hoạch th\xe0nh c\xf4ng: ").concat(l.label)),console.log(l),n(e))};return(0,l.jsx)(H.Z,{checked:0!==t.enable,onChange:e=>s(e),style:{backgroundColor:0!==t.enable?"#45c3a1":"#ccc"}})};let A=e=>new Date(e).toLocaleDateString("vi-VN",{day:"2-digit",month:"2-digit",year:"numeric"});var X=e=>{var t;let[n,a]=(0,i.useState)(!1),[r,o]=(0,i.useState)(0!==e.enable);return(0,l.jsxs)("div",{style:{display:"flex",flexDirection:"column",alignItems:"start",gap:8,border:"1px solid #ddd",padding:16,borderRadius:16,background:r?"rgb(224, 255, 226)":"#fff",width:"100%"},children:[(0,l.jsxs)("div",{style:{width:"100%",display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"space-between",gap:8},children:[(0,l.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:8},children:[(0,l.jsx)("p",{style:{fontSize:16,fontWeight:"bold",margin:0},children:e.label},e.name),(0,l.jsxs)("p",{style:{color:"gray",fontSize:14,margin:0},children:["Thời gian thực hiện: ",A(e.start_date)," -"," ",A(e.end_date)]})]}),(0,l.jsx)(L,{plan:e,setPlanActivated:o})]}),(0,l.jsxs)("div",{style:{marginTop:10,width:"100%",borderRadius:8,boxShadow:"0px 0px 8px rgba(0, 0, 0, 0.1)",border:"1px solid #ddd",display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"space-between",background:"#fff",gap:8,padding:8},children:[(0,l.jsxs)("div",{style:{display:"flex",flexDirection:"row",alignItems:"center",gap:8},children:[(0,l.jsx)("p",{style:{margin:0,color:"gray"},children:"Số lượng chương tr\xecnh c\xe0i đặt:\xa0\xa0"}),(0,l.jsx)("p",{style:{margin:0,fontSize:16,fontWeight:"bold",color:"black"},children:(null==e?void 0:null===(t=e.schedules)||void 0===t?void 0:t.length)||0})]}),(0,l.jsx)(v.ZP,{type:"default",style:{borderRadius:4},size:"large",color:"geekblue",icon:(0,l.jsx)(_.Z,{}),onClick:()=>a(!0)})]}),(0,l.jsx)(w.Z,{title:"Chi tiết kế hoạch",placement:"right",onClose:()=>a(!1),open:n,width:"75%",children:(0,l.jsx)(G,{plan:e,onClose:()=>a(!1)})})]},e.name)},J=e=>{let{onClose:t}=e,[n]=Z.default.useForm(),{deviceId:i}=(0,r.Z)(),{schedulePlans:c,setSchedulePlans:h}=(0,a.Z)(),u=async e=>{let l=T()(e.start_date).startOf("day").format("YYYY-MM-DD"),a=T()(e.end_date).endOf("day").format("YYYY-MM-DD"),r={label:e.label,device_id:i,start_date:l,end_date:a,enable:0},s=await (0,o.Tx)(r);if(null==s?void 0:s.statusOK){var d,u;let l=[...c];l.push(null==s?void 0:null===(u=s.responseData)||void 0===u?void 0:null===(d=u.result)||void 0===d?void 0:d.data),h(l),S.ZP.success("Tạo kế hoạch th\xe0nh c\xf4ng"),n.resetFields(),console.log(e),t()}};return(0,l.jsxs)(Z.default,{form:n,style:{width:"100%"},children:[(0,l.jsxs)("div",{style:{zIndex:100,position:"fixed",bottom:24,right:24,display:"flex",justifyContent:"flex-end",gap:8,padding:8,background:"rgba(255, 255, 255, 0.5)",borderRadius:8,backdropFilter:"blur(5px)",border:"1px solid #ddd",boxShadow:"0px 0px 50px 2px rgba(0, 0, 0, 0.25)"},children:[(0,l.jsx)(v.ZP,{onClick:()=>t(),children:"Hủy"}),(0,l.jsx)(v.ZP,{type:"primary",onClick:()=>u(n.getFieldsValue()),children:"Lưu"})]}),(0,l.jsx)(s.Z,{gutter:[16,16],style:{marginBottom:24},children:(0,l.jsx)(d.Z,{span:24,children:(0,l.jsx)(Z.default.Item,{name:"label",label:"T\xean kế hoạch",rules:[{required:!0}],layout:"vertical",children:(0,l.jsx)(q.Z,{style:{width:"100%"}})})})}),(0,l.jsxs)(s.Z,{gutter:[16,16],style:{marginBottom:32},children:[(0,l.jsx)(d.Z,{span:12,children:(0,l.jsx)(Z.default.Item,{name:"start_date",label:"Ng\xe0y bắt đầu",rules:[{required:!0}],layout:"vertical",children:(0,l.jsx)(k.default,{style:{width:"100%"},placeholder:""})})}),(0,l.jsx)(d.Z,{span:12,children:(0,l.jsx)(Z.default.Item,{name:"end_date",label:"Ng\xe0y kết th\xfac",rules:[{required:!0}],layout:"vertical",children:(0,l.jsx)(k.default,{style:{width:"100%"},placeholder:""})})})]})]})},K=()=>{let[e,t]=(0,i.useState)(null),n=()=>{t(null)};return(0,l.jsxs)("div",{children:[(0,l.jsx)(v.ZP,{type:"primary",style:{position:"fixed",bottom:16,right:16,boxShadow:"0px 2px 50px 2px rgba(0, 0, 0, 0.5)"},icon:(0,l.jsx)(O.Z,{}),size:"large",onClick:()=>{t("plan")}}),(0,l.jsx)(w.Z,{width:"75%",title:"Tạo kế hoạch mới",placement:"right",onClose:n,open:"plan"===e,children:(0,l.jsx)(J,{onClose:n})})]})},Q=n(805),U=n(8211);let $=(0,Q.U)((0,U.n)((e,t)=>({programRunning:[],setProgramRunning:t=>e({programRunning:t})})));var ee=n(508),et=e=>{let{scheduleId:t,label:n,status:r,start_time:o,end_time:s,timestamp:d}=e,{schedulePlans:c}=(0,a.Z)(),[h,u]=(0,i.useState)(null);(0,i.useEffect)(()=>{0!==c.length&&c.forEach(e=>{e.schedules.forEach(e=>{e.id===t&&u(e)})})},[c]);let[g,m]=(0,i.useState)(!1);return(0,l.jsxs)("div",{style:{display:"flex",flexDirection:"column",boxShadow:"0px 0px 8px rgba(0, 0, 0, 0.1)",border:"1px solid #eee",padding:20,borderRadius:16,background:"linear-gradient(to bottom left,rgb(41, 136, 237), 50%, rgb(69, 151, 99))",minHeight:100,overflow:"hidden",position:"relative"},children:[(0,l.jsxs)("div",{style:{display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"space-between"},children:[(0,l.jsxs)("div",{style:{display:"flex",gap:8,flexDirection:"column",alignItems:"start",justifyContent:"space-between"},children:[(0,l.jsx)("p",{style:{fontSize:18,fontWeight:"bold",margin:0,color:"white",cursor:"pointer"},children:n}),(0,l.jsxs)("p",{style:{margin:0,color:"white",fontSize:14},children:["Thời điểm:\xa0\xa0\xa0",(0,l.jsxs)("strong",{children:[T()(o,"HH:mm:ss").format("HH:mm:ss")," -"," ",T()(s,"HH:mm:ss").format("HH:mm:ss")]})]}),(0,l.jsxs)("div",{style:{marginTop:16,display:"flex",flexDirection:"row",alignItems:"center",gap:8},children:[(0,l.jsx)("div",{style:{height:8,width:8,borderRadius:"50%",backgroundColor:"rgb(54, 221, 65)"}}),(0,l.jsx)("p",{style:{fontSize:14,fontWeight:"bold",margin:0,color:"rgb(85, 232, 95)",cursor:"pointer"},children:"running"===r?"Đang hoạt động":"..."})]})]}),(0,l.jsx)(v.ZP,{type:"default",style:{borderRadius:4,zIndex:10},size:"large",color:"geekblue",icon:(0,l.jsx)(_.Z,{}),onClick:()=>m(!0)})]}),(0,l.jsxs)("p",{style:{margin:0,position:"absolute",right:0,top:0,fontSize:150,color:"white",opacity:.1,zIndex:1},children:[" ","> > >"]}),(0,l.jsx)(w.Z,{title:"Chỉnh sửa chương tr\xecnh",open:g,onClose:()=>{m(!1)},width:"70%",children:(0,l.jsx)(ee.Z,{program:h,onClose:()=>{m(!1)},start_date_of_plan:null==h?void 0:h.start_date,end_date_of_plan:null==h?void 0:h.end_date})})]})},en=n(6563),el=n(1421),ei=()=>{let{schedulePlans:e,scheduleProgramTriggerImmediately:t}=(0,a.Z)(),{deviceId:n}=(0,r.Z)(),[o,s]=(0,i.useState)([]);(0,i.useEffect)(()=>{t&&s(t.enum_value.split(",").map(e=>({value:e.trim(),label:e.trim()})))},[t]);let[d,c]=(0,i.useState)([]),[h,u]=(0,i.useState)([]);(0,i.useEffect)(()=>{if(!e)return;let t=[];e.forEach(e=>{e.schedules.forEach(e=>{t.push({value:e,label:e.name})})}),c(t)},[e]);let[g,m]=(0,i.useState)(""),p=e=>{m(e),u(d.filter(t=>{var n;return(null===(n=t.value.action)||void 0===n?void 0:n.env_enum)===e}))},{functionListForControl:x}=(0,r.Z)(),f=e=>{var t;let n=null===(t=x.find(e=>"tb1"===e.identifier))||void 0===t?void 0:t.children,i={label:"",unit:""},a=e=>(null==n||n.forEach(t=>{var n;null==t||null===(n=t.children)||void 0===n||n.forEach(t=>{(null==t?void 0:t.identifier)===e&&(i.label=null==t?void 0:t.label,i.unit=null==t?void 0:t.unit)})}),i);return(0,l.jsx)("div",{style:{width:"100%",display:"flex",flexDirection:"column"},children:Object.entries(e).map(e=>{let[t,n]=e;return"number"!=typeof n&&"true"!==String(n)||"number"==typeof n&&0===n?null:(0,l.jsxs)("div",{style:{width:"100%",display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"space-between",gap:8,borderTop:"1px solid #ddd",padding:4},children:[(0,l.jsx)("p",{style:{margin:0},children:a(t).label}),(0,l.jsxs)("div",{style:{display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"space-between"},children:[(0,l.jsx)("p",{style:{margin:0,fontWeight:0!==n?"bold":"normal"},children:"false"===n?"Tắt":"true"===n?"Bật":n}),(0,l.jsx)("p",{style:{margin:0},children:i.unit})]})]},t)})})},[y,b]=(0,i.useState)(null),[j,v]=(0,i.useState)(!1),w=e=>{b(e),v(!0)},_=()=>{b(null),v(!1)},{run:Z,loading:k}=(0,el.Z)(),I=async()=>{console.log({program_id:y.id,program_name:y.name,device_id:n,type:"trigger_program_immediately",action:y.action});let e=Object.fromEntries(Object.entries(y.action).filter(e=>{let[t,n]=e;return"number"==typeof n&&0!==n||"true"===String(n)}).map(e=>{let[t,n]=e;return[t,"true"===String(n)||"false"!==String(n)&&n]}));await Z({device_id_thingsboard:n,method:"set_state",params:{...e,schedule_id:y.id}}).then(e=>{console.log("ket qua gui env_enum: ",e),S.ZP.success("Đ\xe3 gửi y\xeau cầu k\xedch hoạt chương tr\xecnh !"),_()})};return(0,l.jsxs)("div",{style:{width:"100%",display:"flex",flexDirection:"column"},children:[(0,l.jsx)("p",{style:{fontWeight:"bold",fontSize:16},children:"Chọn nhanh chương tr\xecnh"}),(0,l.jsx)("div",{style:{width:"100%",display:"flex",flexDirection:"row",justifyContent:"space-between",gap:8},children:(0,l.jsx)("div",{style:{display:"flex",flexDirection:"row",gap:8},children:(0,l.jsx)(C.default,{placeholder:"Chọn m\xe3 m\xf4i trường",style:{width:"300px"},onChange:e=>{p(e)},options:o,allowClear:!0})})}),(0,l.jsxs)("div",{style:{marginTop:16,display:"flex",flexDirection:"row",gap:16,overflow:"scroll"},children:[0===h.length&&""!==g?(0,l.jsx)("p",{style:{color:"gray"},children:"Kh\xf4ng c\xf3 chương tr\xecnh ph\xf9 hợp với m\xf4i trường n\xe0y !"}):h.map(e=>(0,l.jsxs)("div",{style:{cursor:"pointer",padding:8,display:"flex",flexDirection:"column",justifyContent:"flex-start",border:"1px solid #ccc",borderRadius:8,background:"linear-gradient(to bottom, #fff, 75%, #eee)"},className:"hoverable",onClick:()=>w(e.value),children:[(0,l.jsx)("p",{style:{fontWeight:"bold"},children:e.label}),(0,l.jsxs)("p",{children:[" ",(0,l.jsx)(en.Z,{style:{marginRight:8}}),T()(e.value.start_time,"HH:mm:ss").format("HH:mm:ss")," ","- ",T()(e.value.end_time,"HH:mm:ss").format("HH:mm:ss")]}),(0,l.jsx)("div",{style:{width:"100%",display:"flex",flexDirection:"column"},children:f(e.value.action)})]},e.value.id)),(0,l.jsx)(Y.Z,{title:"Bạn muốn chạy ngay chương tr\xecnh ".concat(null==y?void 0:y.name," ?"),open:j,onOk:()=>I(),onCancel:()=>_(),onClose:()=>_(),children:(0,l.jsxs)("p",{children:["Chương tr\xecnh n\xe0y sẽ chạy trong v\xf2ng:"," ",(0,E.U)(null==y?void 0:y.start_time,null==y?void 0:y.end_time)," ","Gi\xe2y"]})})]})]})},ea=n(1624),er=n(9960),eo=()=>{let{schedulePlans:e,setSchedulePlans:t}=(0,a.Z)(),{deviceId:n}=(0,r.Z)();(0,i.useEffect)(()=>{n&&e();async function e(){var e;let l=await (0,o.nb)(n);t((null==l?void 0:null===(e=l.result)||void 0===e?void 0:e.data)||[])}},[n]);let{programRunning:c,setProgramRunning:h}=$();(0,i.useEffect)(()=>{console.log("programRunning:",c)},[c]);let u=(0,i.useRef)(c);(0,i.useEffect)(()=>{u.current=c},[c]);let{client:g}=(0,ea.T)();return(0,i.useEffect)(()=>{g.on("message",(e,t)=>{if(e===(0,er.v)(n)){console.log("Received MQTT message on topic ".concat(e,":"),t.toString()),console.log("data from broker for unchart:",t);try{JSON.parse(t.toString()).forEach(e=>{if("active_schedule"===e.key){let t=JSON.parse(e.value);if(console.log("itemValue co neee:",t),u.current.find(e=>e.scheduleId===t.scheduleId)){if("finished"===t.status){let e=u.current.filter(e=>e.scheduleId!==t.scheduleId);console.log("removeProgramRunning:",e),h(JSON.parse(JSON.stringify([...e,t])))}}else"running"===t.status&&(console.log("Adding running program:",t.label),h(JSON.parse(JSON.stringify([...u.current,t]))))}})}catch(e){console.error("MQTT message error:",e)}}})},[e]),(0,l.jsxs)("div",{style:{width:"100%",display:"flex",flexDirection:"column",alignItems:"start",padding:16,gap:16},children:[(0,l.jsx)(s.Z,{gutter:[16,16],style:{width:"100%"},children:(0,l.jsx)(d.Z,{span:24,children:(0,l.jsx)(ei,{})})}),(0,l.jsx)(j,{}),c.length>0&&(0,l.jsxs)("div",{style:{width:"100%",display:"flex",flexDirection:"column",alignItems:"start"},children:[(0,l.jsx)("p",{style:{fontSize:24,fontWeight:"bold",margin:0,marginBottom:16},children:"Chương tr\xecnh đang chạy"}),(0,l.jsx)(s.Z,{gutter:[16,16],style:{width:"100%"},children:c.map(e=>(0,l.jsx)(d.Z,{span:12,children:(0,l.jsx)(et,{scheduleId:e.scheduleId,label:e.label,status:e.status,start_time:e.start_time,end_time:e.end_time,timestamp:e.timestamp})},e.scheduleId))}),(0,l.jsx)(j,{})]}),(0,l.jsx)("p",{style:{fontSize:24,fontWeight:"bold",margin:0},children:"Danh s\xe1ch kế hoạch đ\xe3 tạo"}),(0,l.jsx)(s.Z,{gutter:[16,16],style:{width:"100%"},children:null==e?void 0:e.map(e=>(0,l.jsx)(d.Z,{span:12,children:(0,l.jsx)(X,{...e},e.name)}))}),(0,l.jsx)(K,{})]})},es=()=>(0,l.jsx)(eo,{})},1421:function(e,t,n){"use strict";var l=n(9341),i=n(465);t.Z=function(){let{onSuccess:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(0,i.Q)(l.A2,{onError(e,t){},onSuccess(t,n){null==e||e(t)},manual:!0})}}},function(e){e.O(0,[467,899,478,452,872,295,507,59,563,888,774,179],function(){return e(e.s=6054)}),_N_E=e.O()}]);