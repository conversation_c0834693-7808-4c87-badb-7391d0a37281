"use strict";exports.id=685,exports.ids=[685],exports.modules={21685:(e,t,r)=>{r.d(t,{Z:()=>Z});var o=r(16689),n=r(79712),i=r(81659),l=r(33736),s=r(65904),a=r(6776),c=r(59003),u=r.n(c),d=r(78225),p=r(76393),g=r(50522),m=r(45763),$=r(34053);function f(e){return!e||e<0?0:e>100?100:e}function b(e){let{success:t,successPercent:r}=e,o=r;return t&&"progress"in t&&(o=t.progress),t&&"percent"in t&&(o=t.percent),o}let y=e=>{let{percent:t,success:r,successPercent:o}=e,n=f(b({success:r,successPercent:o}));return[n,f(f(t)-n)]},v=e=>{let{success:t={},strokeColor:r}=e,{strokeColor:o}=t;return[o||$.presetPrimaryColors.green,r||null]},h=(e,t,r)=>{var o,n,i,l;let s=-1,a=-1;if("step"===t){let t=r.steps,o=r.strokeWidth;"string"==typeof e||void 0===e?(s="small"===e?2:14,a=null!=o?o:8):"number"==typeof e?[s,a]=[e,e]:[s=14,a=8]=Array.isArray(e)?e:[e.width,e.height],s*=t}else if("line"===t){let t=null==r?void 0:r.strokeWidth;"string"==typeof e||void 0===e?a=t||("small"===e?6:8):"number"==typeof e?[s,a]=[e,e]:[s=-1,a=8]=Array.isArray(e)?e:[e.width,e.height]}else("circle"===t||"dashboard"===t)&&("string"==typeof e||void 0===e?[s,a]="small"===e?[60,60]:[120,120]:"number"==typeof e?[s,a]=[e,e]:Array.isArray(e)&&(s=null!==(n=null!==(o=e[0])&&void 0!==o?o:e[1])&&void 0!==n?n:120,a=null!==(l=null!==(i=e[0])&&void 0!==i?i:e[1])&&void 0!==l?l:120));return[s,a]},x=e=>3/e*100,C=e=>{let{prefixCls:t,trailColor:r=null,strokeLinecap:n="round",gapPosition:i,gapDegree:l,width:s=120,type:a,children:c,success:d,size:p=s,steps:$}=e,[f,b]=h(p,"circle"),{strokeWidth:C}=e;void 0===C&&(C=Math.max(x(f),6));let S=o.useMemo(()=>l||0===l?l:"dashboard"===a?75:void 0,[l,a]),k=y(e),w="[object Object]"===Object.prototype.toString.call(e.strokeColor),O=v({success:d,strokeColor:e.strokeColor}),E=u()(`${t}-inner`,{[`${t}-circle-gradient`]:w}),j=o.createElement(g.Circle,{steps:$,percent:$?k[1]:k,strokeWidth:C,trailWidth:C,strokeColor:$?O[1]:O,strokeLinecap:n,trailColor:r,prefixCls:t,gapDegree:S,gapPosition:i||"dashboard"===a&&"bottom"||void 0}),I=f<=20,A=o.createElement("div",{className:E,style:{width:f,height:b,fontSize:.15*f+6}},j,!I&&c);return I?o.createElement(m.Z,{title:c},A):A};var S=r(52727),k=r(92929),w=r(83505),O=r(10045);let E="--progress-line-stroke-color",j="--progress-percent",I=e=>{let t=e?"100%":"-100%";return new S.Keyframes(`antProgress${e?"RTL":"LTR"}Active`,{"0%":{transform:`translateX(${t}) scaleX(0)`,opacity:.1},"20%":{transform:`translateX(${t}) scaleX(0)`,opacity:.5},to:{transform:"translateX(0) scaleX(1)",opacity:0}})},A=e=>{let{componentCls:t,iconCls:r}=e;return{[t]:Object.assign(Object.assign({},(0,k.Wf)(e)),{display:"inline-block","&-rtl":{direction:"rtl"},"&-line":{position:"relative",width:"100%",fontSize:e.fontSize},[`${t}-outer`]:{display:"inline-flex",alignItems:"center",width:"100%"},[`${t}-inner`]:{position:"relative",display:"inline-block",width:"100%",flex:1,overflow:"hidden",verticalAlign:"middle",backgroundColor:e.remainingColor,borderRadius:e.lineBorderRadius},[`${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.defaultColor}},[`${t}-success-bg, ${t}-bg`]:{position:"relative",background:e.defaultColor,borderRadius:e.lineBorderRadius,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOutCirc}`},[`${t}-layout-bottom`]:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",[`${t}-text`]:{width:"max-content",marginInlineStart:0,marginTop:e.marginXXS}},[`${t}-bg`]:{overflow:"hidden","&::after":{content:'""',background:{_multi_value_:!0,value:["inherit",`var(${E})`]},height:"100%",width:`calc(1 / var(${j}) * 100%)`,display:"block"},[`&${t}-bg-inner`]:{minWidth:"max-content","&::after":{content:"none"},[`${t}-text-inner`]:{color:e.colorWhite,[`&${t}-text-bright`]:{color:"rgba(0, 0, 0, 0.45)"}}}},[`${t}-success-bg`]:{position:"absolute",insetBlockStart:0,insetInlineStart:0,backgroundColor:e.colorSuccess},[`${t}-text`]:{display:"inline-block",marginInlineStart:e.marginXS,color:e.colorText,lineHeight:1,width:"2em",whiteSpace:"nowrap",textAlign:"start",verticalAlign:"middle",wordBreak:"normal",[r]:{fontSize:e.fontSize},[`&${t}-text-outer`]:{width:"max-content"},[`&${t}-text-outer${t}-text-start`]:{width:"max-content",marginInlineStart:0,marginInlineEnd:e.marginXS}},[`${t}-text-inner`]:{display:"flex",justifyContent:"center",alignItems:"center",width:"100%",height:"100%",marginInlineStart:0,padding:`0 ${(0,S.unit)(e.paddingXXS)}`,[`&${t}-text-start`]:{justifyContent:"start"},[`&${t}-text-end`]:{justifyContent:"end"}},[`&${t}-status-active`]:{[`${t}-bg::before`]:{position:"absolute",inset:0,backgroundColor:e.colorBgContainer,borderRadius:e.lineBorderRadius,opacity:0,animationName:I(),animationDuration:e.progressActiveMotionDuration,animationTimingFunction:e.motionEaseOutQuint,animationIterationCount:"infinite",content:'""'}},[`&${t}-rtl${t}-status-active`]:{[`${t}-bg::before`]:{animationName:I(!0)}},[`&${t}-status-exception`]:{[`${t}-bg`]:{backgroundColor:e.colorError},[`${t}-text`]:{color:e.colorError}},[`&${t}-status-exception ${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.colorError}},[`&${t}-status-success`]:{[`${t}-bg`]:{backgroundColor:e.colorSuccess},[`${t}-text`]:{color:e.colorSuccess}},[`&${t}-status-success ${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.colorSuccess}}})}},N=e=>{let{componentCls:t,iconCls:r}=e;return{[t]:{[`${t}-circle-trail`]:{stroke:e.remainingColor},[`&${t}-circle ${t}-inner`]:{position:"relative",lineHeight:1,backgroundColor:"transparent"},[`&${t}-circle ${t}-text`]:{position:"absolute",insetBlockStart:"50%",insetInlineStart:0,width:"100%",margin:0,padding:0,color:e.circleTextColor,fontSize:e.circleTextFontSize,lineHeight:1,whiteSpace:"normal",textAlign:"center",transform:"translateY(-50%)",[r]:{fontSize:e.circleIconFontSize}},[`${t}-circle&-status-exception`]:{[`${t}-text`]:{color:e.colorError}},[`${t}-circle&-status-success`]:{[`${t}-text`]:{color:e.colorSuccess}}},[`${t}-inline-circle`]:{lineHeight:1,[`${t}-inner`]:{verticalAlign:"bottom"}}}},P=e=>{let{componentCls:t}=e;return{[t]:{[`${t}-steps`]:{display:"inline-block","&-outer":{display:"flex",flexDirection:"row",alignItems:"center"},"&-item":{flexShrink:0,minWidth:e.progressStepMinWidth,marginInlineEnd:e.progressStepMarginInlineEnd,backgroundColor:e.remainingColor,transition:`all ${e.motionDurationSlow}`,"&-active":{backgroundColor:e.defaultColor}}}}}},z=e=>{let{componentCls:t,iconCls:r}=e;return{[t]:{[`${t}-small&-line, ${t}-small&-line ${t}-text ${r}`]:{fontSize:e.fontSizeSM}}}},M=(0,w.I$)("Progress",e=>{let t=e.calc(e.marginXXS).div(2).equal(),r=(0,O.mergeToken)(e,{progressStepMarginInlineEnd:t,progressStepMinWidth:t,progressActiveMotionDuration:"2.4s"});return[A(r),N(r),P(r),z(r)]},e=>({circleTextColor:e.colorText,defaultColor:e.colorInfo,remainingColor:e.colorFillSecondary,lineBorderRadius:100,circleTextFontSize:"1em",circleIconFontSize:`${e.fontSize/e.fontSizeSM}em`}));var X=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(r[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,o=Object.getOwnPropertySymbols(e);n<o.length;n++)0>t.indexOf(o[n])&&Object.prototype.propertyIsEnumerable.call(e,o[n])&&(r[o[n]]=e[o[n]]);return r};let T=e=>{let t=[];return Object.keys(e).forEach(r=>{let o=parseFloat(r.replace(/%/g,""));Number.isNaN(o)||t.push({key:o,value:e[r]})}),(t=t.sort((e,t)=>e.key-t.key)).map(e=>{let{key:t,value:r}=e;return`${r} ${t}%`}).join(", ")},W=(e,t)=>{let{from:r=$.presetPrimaryColors.blue,to:o=$.presetPrimaryColors.blue,direction:n="rtl"===t?"to left":"to right"}=e,i=X(e,["from","to","direction"]);if(0!==Object.keys(i).length){let e=T(i),t=`linear-gradient(${n}, ${e})`;return{background:t,[E]:t}}let l=`linear-gradient(${n}, ${r}, ${o})`;return{background:l,[E]:l}},R=e=>{let{prefixCls:t,direction:r,percent:n,size:i,strokeWidth:l,strokeColor:s,strokeLinecap:a="round",children:c,trailColor:d=null,percentPosition:p,success:g}=e,{align:m,type:$}=p,y=s&&"string"!=typeof s?W(s,r):{[E]:s,background:s},v="square"===a||"butt"===a?0:void 0,[x,C]=h(null!=i?i:[-1,l||("small"===i?6:8)],"line",{strokeWidth:l}),S=Object.assign(Object.assign({width:`${f(n)}%`,height:C,borderRadius:v},y),{[j]:f(n)/100}),k=b(e),w={width:`${f(k)}%`,height:C,borderRadius:v,backgroundColor:null==g?void 0:g.strokeColor},O=o.createElement("div",{className:`${t}-inner`,style:{backgroundColor:d||void 0,borderRadius:v}},o.createElement("div",{className:u()(`${t}-bg`,`${t}-bg-${$}`),style:S},"inner"===$&&c),void 0!==k&&o.createElement("div",{className:`${t}-success-bg`,style:w})),I="outer"===$&&"start"===m,A="outer"===$&&"end"===m;return"outer"===$&&"center"===m?o.createElement("div",{className:`${t}-layout-bottom`},O,c):o.createElement("div",{className:`${t}-outer`,style:{width:x<0?"100%":x}},I&&c,O,A&&c)},D=e=>{let{size:t,steps:r,rounding:n=Math.round,percent:i=0,strokeWidth:l=8,strokeColor:s,trailColor:a=null,prefixCls:c,children:d}=e,p=n(i/100*r),[g,m]=h(null!=t?t:["small"===t?2:14,l],"step",{steps:r,strokeWidth:l}),$=g/r,f=Array.from({length:r});for(let e=0;e<r;e++){let t=Array.isArray(s)?s[e]:s;f[e]=o.createElement("div",{key:e,className:u()(`${c}-steps-item`,{[`${c}-steps-item-active`]:e<=p-1}),style:{backgroundColor:e<=p-1?t:a,width:$,height:m}})}return o.createElement("div",{className:`${c}-steps-outer`},f,d)};var B=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(r[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,o=Object.getOwnPropertySymbols(e);n<o.length;n++)0>t.indexOf(o[n])&&Object.prototype.propertyIsEnumerable.call(e,o[n])&&(r[o[n]]=e[o[n]]);return r};let F=["normal","exception","active","success"],Z=o.forwardRef((e,t)=>{let r;let{prefixCls:c,className:g,rootClassName:m,steps:$,strokeColor:y,percent:v=0,size:x="default",showInfo:S=!0,type:k="line",status:w,format:O,style:E,percentPosition:j={}}=e,I=B(e,["prefixCls","className","rootClassName","steps","strokeColor","percent","size","showInfo","type","status","format","style","percentPosition"]),{align:A="end",type:N="outer"}=j,P=Array.isArray(y)?y[0]:y,z="string"==typeof y||Array.isArray(y)?y:void 0,X=o.useMemo(()=>{if(P){let e="string"==typeof P?P:Object.values(P)[0];return new n.FastColor(e).isLight()}return!1},[y]),T=o.useMemo(()=>{var t,r;let o=b(e);return parseInt(void 0!==o?null===(t=null!=o?o:0)||void 0===t?void 0:t.toString():null===(r=null!=v?v:0)||void 0===r?void 0:r.toString(),10)},[v,e.success,e.successPercent]),W=o.useMemo(()=>!F.includes(w)&&T>=100?"success":w||"normal",[w,T]),{getPrefixCls:Z,direction:L,progress:H}=o.useContext(p.E_),_=Z("progress",c),[q,K,Q]=M(_),Y="line"===k,G=Y&&!$,J=o.useMemo(()=>{let t;if(!S)return null;let r=b(e),n=O||(e=>`${e}%`),c=Y&&X&&"inner"===N;return"inner"===N||O||"exception"!==W&&"success"!==W?t=n(f(v),f(r)):"exception"===W?t=Y?o.createElement(s.Z,null):o.createElement(a.Z,null):"success"===W&&(t=Y?o.createElement(i.Z,null):o.createElement(l.Z,null)),o.createElement("span",{className:u()(`${_}-text`,{[`${_}-text-bright`]:c,[`${_}-text-${A}`]:G,[`${_}-text-${N}`]:G}),title:"string"==typeof t?t:void 0},t)},[S,v,T,W,k,_,O]);"line"===k?r=$?o.createElement(D,Object.assign({},e,{strokeColor:z,prefixCls:_,steps:"object"==typeof $?$.count:$}),J):o.createElement(R,Object.assign({},e,{strokeColor:P,prefixCls:_,direction:L,percentPosition:{align:A,type:N}}),J):("circle"===k||"dashboard"===k)&&(r=o.createElement(C,Object.assign({},e,{strokeColor:P,prefixCls:_,progressStatus:W}),J));let U=u()(_,`${_}-status-${W}`,{[`${_}-${"dashboard"===k&&"circle"||k}`]:"line"!==k,[`${_}-inline-circle`]:"circle"===k&&h(x,"circle")[0]<=20,[`${_}-line`]:G,[`${_}-line-align-${A}`]:G,[`${_}-line-position-${N}`]:G,[`${_}-steps`]:$,[`${_}-show-info`]:S,[`${_}-${x}`]:"string"==typeof x,[`${_}-rtl`]:"rtl"===L},null==H?void 0:H.className,g,m,K,Q);return q(o.createElement("div",Object.assign({ref:t,style:Object.assign(Object.assign({},null==H?void 0:H.style),E),className:U,role:"progressbar","aria-valuenow":T,"aria-valuemin":0,"aria-valuemax":100},(0,d.Z)(I,["trailColor","strokeWidth","width","gapDegree","gapPosition","strokeLinecap","success","successPercent"])),r))})}};