"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[456],{6111:function(t,e,n){n.d(e,{Z:function(){return i}});var a=n(5773),o=n(7378),c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"}},{tag:"path",attrs:{d:"M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"}}]},name:"plus",theme:"outlined"},r=n(3359),i=o.forwardRef(function(t,e){return o.createElement(r.Z,(0,a.Z)({},t,{ref:e,icon:c}))})},4326:function(t,e){e.Z=()=>null},4456:function(t,e,n){n.d(e,{Z:function(){return tS}});var a=n(7378),o=n(6180),c=n(2608),r=n(6111),i=n(5),l=n.n(i),s=n(5773),u=n(4649),d=n(189),f=n(8136),v=n(3940),p=n(6535),b=n(9270),m=n(8479),h=(0,a.createContext)(null),g=n(3285),y=n(7220),k=n(8101),w=n(7861),x=n(4978),_=function(t){var e=t.activeTabOffset,n=t.horizontal,o=t.rtl,c=t.indicator,r=void 0===c?{}:c,i=r.size,l=r.align,s=void 0===l?"center":l,u=(0,a.useState)(),d=(0,f.Z)(u,2),v=d[0],p=d[1],b=(0,a.useRef)(),m=a.useCallback(function(t){return"function"==typeof i?i(t):"number"==typeof i?i:t},[i]);function h(){x.Z.cancel(b.current)}return(0,a.useEffect)(function(){var t={};if(e){if(n){t.width=m(e.width);var a=o?"right":"left";"start"===s&&(t[a]=e[a]),"center"===s&&(t[a]=e[a]+e.width/2,t.transform=o?"translateX(50%)":"translateX(-50%)"),"end"===s&&(t[a]=e[a]+e.width,t.transform="translateX(-100%)")}else t.height=m(e.height),"start"===s&&(t.top=e.top),"center"===s&&(t.top=e.top+e.height/2,t.transform="translateY(-50%)"),"end"===s&&(t.top=e.top+e.height,t.transform="translateY(-100%)")}return h(),b.current=(0,x.Z)(function(){p(t)}),h},[e,n,o,s,m]),{style:v}},S={width:0,height:0,left:0,top:0};function C(t,e){var n=a.useRef(t),o=a.useState({}),c=(0,f.Z)(o,2)[1];return[n.current,function(t){var a="function"==typeof t?t(n.current):t;a!==n.current&&e(a,n.current),n.current=a,c({})}]}var E=n(4812);function Z(t){var e=(0,a.useState)(0),n=(0,f.Z)(e,2),o=n[0],c=n[1],r=(0,a.useRef)(0),i=(0,a.useRef)();return i.current=t,(0,E.o)(function(){var t;null===(t=i.current)||void 0===t||t.call(i)},[o]),function(){r.current===o&&(r.current+=1,c(r.current))}}var R={width:0,height:0,left:0,top:0,right:0};function P(t){var e;return t instanceof Map?(e={},t.forEach(function(t,n){e[n]=t})):e=t,JSON.stringify(e)}function T(t){return String(t).replace(/"/g,"TABS_DQ")}function M(t,e,n,a){return!!n&&!a&&!1!==t&&(void 0!==t||!1!==e&&null!==e)}var O=a.forwardRef(function(t,e){var n=t.prefixCls,o=t.editable,c=t.locale,r=t.style;return o&&!1!==o.showAdd?a.createElement("button",{ref:e,type:"button",className:"".concat(n,"-nav-add"),style:r,"aria-label":(null==c?void 0:c.addAriaLabel)||"Add tab",onClick:function(t){o.onEdit("add",{event:t})}},o.addIcon||"+"):null}),I=a.forwardRef(function(t,e){var n,o=t.position,c=t.prefixCls,r=t.extra;if(!r)return null;var i={};return"object"!==(0,v.Z)(r)||a.isValidElement(r)?i.right=r:i=r,"right"===o&&(n=i.right),"left"===o&&(n=i.left),n?a.createElement("div",{className:"".concat(c,"-extra-content"),ref:e},n):null}),L=n(8698),N=n(894),D=n(7237),B=a.forwardRef(function(t,e){var n=t.prefixCls,o=t.id,c=t.tabs,r=t.locale,i=t.mobile,d=t.more,v=void 0===d?{}:d,p=t.style,b=t.className,m=t.editable,h=t.tabBarGutter,g=t.rtl,y=t.removeAriaLabel,k=t.onTabClick,w=t.getPopupContainer,x=t.popupClassName,_=(0,a.useState)(!1),S=(0,f.Z)(_,2),C=S[0],E=S[1],Z=(0,a.useState)(null),R=(0,f.Z)(Z,2),P=R[0],T=R[1],I=v.icon,B="".concat(o,"-more-popup"),z="".concat(n,"-dropdown"),j=null!==P?"".concat(B,"-").concat(P):null,A=null==r?void 0:r.dropdownAriaLabel,W=a.createElement(N.ZP,{onClick:function(t){k(t.key,t.domEvent),E(!1)},prefixCls:"".concat(z,"-menu"),id:B,tabIndex:-1,role:"listbox","aria-activedescendant":j,selectedKeys:[P],"aria-label":void 0!==A?A:"expanded dropdown"},c.map(function(t){var e=t.closable,n=t.disabled,c=t.closeIcon,r=t.key,i=t.label,l=M(e,c,m,n);return a.createElement(N.sN,{key:r,id:"".concat(B,"-").concat(r),role:"option","aria-controls":o&&"".concat(o,"-panel-").concat(r),disabled:n},a.createElement("span",null,i),l&&a.createElement("button",{type:"button","aria-label":y||"remove",tabIndex:0,className:"".concat(z,"-menu-item-remove"),onClick:function(t){t.stopPropagation(),t.preventDefault(),t.stopPropagation(),m.onEdit("remove",{key:r,event:t})}},c||m.removeIcon||"\xd7"))}));function G(t){for(var e=c.filter(function(t){return!t.disabled}),n=e.findIndex(function(t){return t.key===P})||0,a=e.length,o=0;o<a;o+=1){var r=e[n=(n+t+a)%a];if(!r.disabled){T(r.key);return}}}(0,a.useEffect)(function(){var t=document.getElementById(j);t&&t.scrollIntoView&&t.scrollIntoView(!1)},[P]),(0,a.useEffect)(function(){C||T(null)},[C]);var X=(0,u.Z)({},g?"marginRight":"marginLeft",h);c.length||(X.visibility="hidden",X.order=1);var H=l()((0,u.Z)({},"".concat(z,"-rtl"),g)),K=i?null:a.createElement(L.Z,(0,s.Z)({prefixCls:z,overlay:W,visible:!!c.length&&C,onVisibleChange:E,overlayClassName:l()(H,x),mouseEnterDelay:.1,mouseLeaveDelay:.1,getPopupContainer:w},v),a.createElement("button",{type:"button",className:"".concat(n,"-nav-more"),style:X,"aria-haspopup":"listbox","aria-controls":B,id:"".concat(o,"-more"),"aria-expanded":C,onKeyDown:function(t){var e=t.which;if(!C){[D.Z.DOWN,D.Z.SPACE,D.Z.ENTER].includes(e)&&(E(!0),t.preventDefault());return}switch(e){case D.Z.UP:G(-1),t.preventDefault();break;case D.Z.DOWN:G(1),t.preventDefault();break;case D.Z.ESC:E(!1);break;case D.Z.SPACE:case D.Z.ENTER:null!==P&&k(P,t)}}},void 0===I?"More":I));return a.createElement("div",{className:l()("".concat(n,"-nav-operations"),b),style:p,ref:e},K,a.createElement(O,{prefixCls:n,locale:r,editable:m}))}),z=a.memo(B,function(t,e){return e.tabMoving}),j=function(t){var e=t.prefixCls,n=t.id,o=t.active,c=t.focus,r=t.tab,i=r.key,s=r.label,d=r.disabled,f=r.closeIcon,v=r.icon,p=t.closable,b=t.renderWrapper,m=t.removeAriaLabel,h=t.editable,g=t.onClick,y=t.onFocus,k=t.onBlur,w=t.onKeyDown,x=t.onMouseDown,_=t.onMouseUp,S=t.style,C=t.tabCount,E=t.currentPosition,Z="".concat(e,"-tab"),R=M(p,f,h,d);function P(t){d||g(t)}var O=a.useMemo(function(){return v&&"string"==typeof s?a.createElement("span",null,s):s},[s,v]),I=a.useRef(null);a.useEffect(function(){c&&I.current&&I.current.focus()},[c]);var L=a.createElement("div",{key:i,"data-node-key":T(i),className:l()(Z,(0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)({},"".concat(Z,"-with-remove"),R),"".concat(Z,"-active"),o),"".concat(Z,"-disabled"),d),"".concat(Z,"-focus"),c)),style:S,onClick:P},a.createElement("div",{ref:I,role:"tab","aria-selected":o,id:n&&"".concat(n,"-tab-").concat(i),className:"".concat(Z,"-btn"),"aria-controls":n&&"".concat(n,"-panel-").concat(i),"aria-disabled":d,tabIndex:d?null:o?0:-1,onClick:function(t){t.stopPropagation(),P(t)},onKeyDown:w,onMouseDown:x,onMouseUp:_,onFocus:y,onBlur:k},c&&a.createElement("div",{"aria-live":"polite",style:{width:0,height:0,position:"absolute",overflow:"hidden",opacity:0}},"Tab ".concat(E," of ").concat(C)),v&&a.createElement("span",{className:"".concat(Z,"-icon")},v),s&&O),R&&a.createElement("button",{type:"button",role:"tab","aria-label":m||"remove",tabIndex:o?0:-1,className:"".concat(Z,"-remove"),onClick:function(t){t.stopPropagation(),t.preventDefault(),t.stopPropagation(),h.onEdit("remove",{key:i,event:t})}},f||h.removeIcon||"\xd7"));return b?b(L):L},A=function(t,e){var n=t.offsetWidth,a=t.offsetHeight,o=t.offsetTop,c=t.offsetLeft,r=t.getBoundingClientRect(),i=r.width,l=r.height,s=r.left,u=r.top;return 1>Math.abs(i-n)?[i,l,s-e.left,u-e.top]:[n,a,c,o]},W=function(t){var e=t.current||{},n=e.offsetWidth,a=void 0===n?0:n,o=e.offsetHeight;if(t.current){var c=t.current.getBoundingClientRect(),r=c.width,i=c.height;if(1>Math.abs(r-a))return[r,i]}return[a,void 0===o?0:o]},G=function(t,e){return t[e?0:1]},X=a.forwardRef(function(t,e){var n,o,c,r,i,v,p,b,m,x,E,L,N,D,B,X,H,K,q,F,V,Y,Q,U,J,$,tt,te,tn,ta,to,tc,tr,ti,tl,ts,tu,td,tf,tv=t.className,tp=t.style,tb=t.id,tm=t.animated,th=t.activeKey,tg=t.rtl,ty=t.extra,tk=t.editable,tw=t.locale,tx=t.tabPosition,t_=t.tabBarGutter,tS=t.children,tC=t.onTabClick,tE=t.onTabScroll,tZ=t.indicator,tR=a.useContext(h),tP=tR.prefixCls,tT=tR.tabs,tM=(0,a.useRef)(null),tO=(0,a.useRef)(null),tI=(0,a.useRef)(null),tL=(0,a.useRef)(null),tN=(0,a.useRef)(null),tD=(0,a.useRef)(null),tB=(0,a.useRef)(null),tz="top"===tx||"bottom"===tx,tj=C(0,function(t,e){tz&&tE&&tE({direction:t>e?"left":"right"})}),tA=(0,f.Z)(tj,2),tW=tA[0],tG=tA[1],tX=C(0,function(t,e){!tz&&tE&&tE({direction:t>e?"top":"bottom"})}),tH=(0,f.Z)(tX,2),tK=tH[0],tq=tH[1],tF=(0,a.useState)([0,0]),tV=(0,f.Z)(tF,2),tY=tV[0],tQ=tV[1],tU=(0,a.useState)([0,0]),tJ=(0,f.Z)(tU,2),t$=tJ[0],t0=tJ[1],t1=(0,a.useState)([0,0]),t2=(0,f.Z)(t1,2),t8=t2[0],t4=t2[1],t7=(0,a.useState)([0,0]),t6=(0,f.Z)(t7,2),t5=t6[0],t9=t6[1],t3=(n=new Map,o=(0,a.useRef)([]),c=(0,a.useState)({}),r=(0,f.Z)(c,2)[1],i=(0,a.useRef)("function"==typeof n?n():n),v=Z(function(){var t=i.current;o.current.forEach(function(e){t=e(t)}),o.current=[],i.current=t,r({})}),[i.current,function(t){o.current.push(t),v()}]),et=(0,f.Z)(t3,2),ee=et[0],en=et[1],ea=(p=t$[0],(0,a.useMemo)(function(){for(var t=new Map,e=ee.get(null===(o=tT[0])||void 0===o?void 0:o.key)||S,n=e.left+e.width,a=0;a<tT.length;a+=1){var o,c,r=tT[a].key,i=ee.get(r);i||(i=ee.get(null===(c=tT[a-1])||void 0===c?void 0:c.key)||S);var l=t.get(r)||(0,d.Z)({},i);l.right=n-l.left-l.width,t.set(r,l)}return t},[tT.map(function(t){return t.key}).join("_"),ee,p])),eo=G(tY,tz),ec=G(t$,tz),er=G(t8,tz),ei=G(t5,tz),el=Math.floor(eo)<Math.floor(ec+er),es=el?eo-ei:eo-er,eu="".concat(tP,"-nav-operations-hidden"),ed=0,ef=0;function ev(t){return t<ed?ed:t>ef?ef:t}tz&&tg?(ed=0,ef=Math.max(0,ec-es)):(ed=Math.min(0,es-ec),ef=0);var ep=(0,a.useRef)(null),eb=(0,a.useState)(),em=(0,f.Z)(eb,2),eh=em[0],eg=em[1];function ey(){eg(Date.now())}function ek(){ep.current&&clearTimeout(ep.current)}b=function(t,e){function n(t,e){t(function(t){return ev(t+e)})}return!!el&&(tz?n(tG,t):n(tq,e),ek(),ey(),!0)},m=(0,a.useState)(),E=(x=(0,f.Z)(m,2))[0],L=x[1],N=(0,a.useState)(0),B=(D=(0,f.Z)(N,2))[0],X=D[1],H=(0,a.useState)(0),q=(K=(0,f.Z)(H,2))[0],F=K[1],V=(0,a.useState)(),Q=(Y=(0,f.Z)(V,2))[0],U=Y[1],J=(0,a.useRef)(),$=(0,a.useRef)(),(tt=(0,a.useRef)(null)).current={onTouchStart:function(t){var e=t.touches[0];L({x:e.screenX,y:e.screenY}),window.clearInterval(J.current)},onTouchMove:function(t){if(E){var e=t.touches[0],n=e.screenX,a=e.screenY;L({x:n,y:a});var o=n-E.x,c=a-E.y;b(o,c);var r=Date.now();X(r),F(r-B),U({x:o,y:c})}},onTouchEnd:function(){if(E&&(L(null),U(null),Q)){var t=Q.x/q,e=Q.y/q;if(!(.1>Math.max(Math.abs(t),Math.abs(e)))){var n=t,a=e;J.current=window.setInterval(function(){if(.01>Math.abs(n)&&.01>Math.abs(a)){window.clearInterval(J.current);return}n*=.9046104802746175,a*=.9046104802746175,b(20*n,20*a)},20)}}},onWheel:function(t){var e=t.deltaX,n=t.deltaY,a=0,o=Math.abs(e),c=Math.abs(n);o===c?a="x"===$.current?e:n:o>c?(a=e,$.current="x"):(a=n,$.current="y"),b(-a,-a)&&t.preventDefault()}},a.useEffect(function(){function t(t){tt.current.onTouchMove(t)}function e(t){tt.current.onTouchEnd(t)}return document.addEventListener("touchmove",t,{passive:!1}),document.addEventListener("touchend",e,{passive:!0}),tL.current.addEventListener("touchstart",function(t){tt.current.onTouchStart(t)},{passive:!0}),tL.current.addEventListener("wheel",function(t){tt.current.onWheel(t)},{passive:!1}),function(){document.removeEventListener("touchmove",t),document.removeEventListener("touchend",e)}},[]),(0,a.useEffect)(function(){return ek(),eh&&(ep.current=setTimeout(function(){eg(0)},100)),ek},[eh]);var ew=(te=tz?tW:tK,tr=(tn=(0,d.Z)((0,d.Z)({},t),{},{tabs:tT})).tabs,ti=tn.tabPosition,tl=tn.rtl,["top","bottom"].includes(ti)?(ta="width",to=tl?"right":"left",tc=Math.abs(te)):(ta="height",to="top",tc=-te),(0,a.useMemo)(function(){if(!tr.length)return[0,0];for(var t=tr.length,e=t,n=0;n<t;n+=1){var a=ea.get(tr[n].key)||R;if(Math.floor(a[to]+a[ta])>Math.floor(tc+es)){e=n-1;break}}for(var o=0,c=t-1;c>=0;c-=1)if((ea.get(tr[c].key)||R)[to]<tc){o=c+1;break}return o>=e?[0,0]:[o,e]},[ea,es,ec,er,ei,tc,ti,tr.map(function(t){return t.key}).join("_"),tl])),ex=(0,f.Z)(ew,2),e_=ex[0],eS=ex[1],eC=(0,k.Z)(function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:th,e=ea.get(t)||{width:0,height:0,left:0,right:0,top:0};if(tz){var n=tW;tg?e.right<tW?n=e.right:e.right+e.width>tW+es&&(n=e.right+e.width-es):e.left<-tW?n=-e.left:e.left+e.width>-tW+es&&(n=-(e.left+e.width-es)),tq(0),tG(ev(n))}else{var a=tK;e.top<-tK?a=-e.top:e.top+e.height>-tK+es&&(a=-(e.top+e.height-es)),tG(0),tq(ev(a))}}),eE=(0,a.useState)(),eZ=(0,f.Z)(eE,2),eR=eZ[0],eP=eZ[1],eT=(0,a.useState)(!1),eM=(0,f.Z)(eT,2),eO=eM[0],eI=eM[1],eL=tT.filter(function(t){return!t.disabled}).map(function(t){return t.key}),eN=function(t){var e=eL.indexOf(eR||th),n=eL.length;eP(eL[(e+t+n)%n])},eD=function(t){var e=t.code,n=tg&&tz,a=eL[0],o=eL[eL.length-1];switch(e){case"ArrowLeft":tz&&eN(n?1:-1);break;case"ArrowRight":tz&&eN(n?-1:1);break;case"ArrowUp":t.preventDefault(),tz||eN(-1);break;case"ArrowDown":t.preventDefault(),tz||eN(1);break;case"Home":t.preventDefault(),eP(a);break;case"End":t.preventDefault(),eP(o);break;case"Enter":case"Space":t.preventDefault(),tC(th,t);break;case"Backspace":case"Delete":var c=eL.indexOf(eR),r=tT.find(function(t){return t.key===eR});M(null==r?void 0:r.closable,null==r?void 0:r.closeIcon,tk,null==r?void 0:r.disabled)&&(t.preventDefault(),t.stopPropagation(),tk.onEdit("remove",{key:eR,event:t}),c===eL.length-1?eN(-1):eN(1))}},eB={};tz?eB[tg?"marginRight":"marginLeft"]=t_:eB.marginTop=t_;var ez=tT.map(function(t,e){var n=t.key;return a.createElement(j,{id:tb,prefixCls:tP,key:n,tab:t,style:0===e?void 0:eB,closable:t.closable,editable:tk,active:n===th,focus:n===eR,renderWrapper:tS,removeAriaLabel:null==tw?void 0:tw.removeAriaLabel,tabCount:eL.length,currentPosition:e+1,onClick:function(t){tC(n,t)},onKeyDown:eD,onFocus:function(){eO||eP(n),eC(n),ey(),tL.current&&(tg||(tL.current.scrollLeft=0),tL.current.scrollTop=0)},onBlur:function(){eP(void 0)},onMouseDown:function(){eI(!0)},onMouseUp:function(){eI(!1)}})}),ej=function(){return en(function(){var t,e=new Map,n=null===(t=tN.current)||void 0===t?void 0:t.getBoundingClientRect();return tT.forEach(function(t){var a,o=t.key,c=null===(a=tN.current)||void 0===a?void 0:a.querySelector('[data-node-key="'.concat(T(o),'"]'));if(c){var r=A(c,n),i=(0,f.Z)(r,4),l=i[0],s=i[1],u=i[2],d=i[3];e.set(o,{width:l,height:s,left:u,top:d})}}),e})};(0,a.useEffect)(function(){ej()},[tT.map(function(t){return t.key}).join("_")]);var eA=Z(function(){var t=W(tM),e=W(tO),n=W(tI);tQ([t[0]-e[0]-n[0],t[1]-e[1]-n[1]]);var a=W(tB);t4(a),t9(W(tD));var o=W(tN);t0([o[0]-a[0],o[1]-a[1]]),ej()}),eW=tT.slice(0,e_),eG=tT.slice(eS+1),eX=[].concat((0,g.Z)(eW),(0,g.Z)(eG)),eH=ea.get(th),eK=_({activeTabOffset:eH,horizontal:tz,indicator:tZ,rtl:tg}).style;(0,a.useEffect)(function(){eC()},[th,ed,ef,P(eH),P(ea),tz]),(0,a.useEffect)(function(){eA()},[tg]);var eq=!!eX.length,eF="".concat(tP,"-nav-wrap");return tz?tg?(tu=tW>0,ts=tW!==ef):(ts=tW<0,tu=tW!==ed):(td=tK<0,tf=tK!==ed),a.createElement(y.Z,{onResize:eA},a.createElement("div",{ref:(0,w.x1)(e,tM),role:"tablist","aria-orientation":tz?"horizontal":"vertical",className:l()("".concat(tP,"-nav"),tv),style:tp,onKeyDown:function(){ey()}},a.createElement(I,{ref:tO,position:"left",extra:ty,prefixCls:tP}),a.createElement(y.Z,{onResize:eA},a.createElement("div",{className:l()(eF,(0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)({},"".concat(eF,"-ping-left"),ts),"".concat(eF,"-ping-right"),tu),"".concat(eF,"-ping-top"),td),"".concat(eF,"-ping-bottom"),tf)),ref:tL},a.createElement(y.Z,{onResize:eA},a.createElement("div",{ref:tN,className:"".concat(tP,"-nav-list"),style:{transform:"translate(".concat(tW,"px, ").concat(tK,"px)"),transition:eh?"none":void 0}},ez,a.createElement(O,{ref:tB,prefixCls:tP,locale:tw,editable:tk,style:(0,d.Z)((0,d.Z)({},0===ez.length?void 0:eB),{},{visibility:eq?"hidden":null})}),a.createElement("div",{className:l()("".concat(tP,"-ink-bar"),(0,u.Z)({},"".concat(tP,"-ink-bar-animated"),tm.inkBar)),style:eK}))))),a.createElement(z,(0,s.Z)({},t,{removeAriaLabel:null==tw?void 0:tw.removeAriaLabel,ref:tD,prefixCls:tP,tabs:eX,className:!eq&&eu,tabMoving:!!eh})),a.createElement(I,{ref:tI,position:"right",extra:ty,prefixCls:tP})))}),H=a.forwardRef(function(t,e){var n=t.prefixCls,o=t.className,c=t.style,r=t.id,i=t.active,s=t.tabKey,u=t.children;return a.createElement("div",{id:r&&"".concat(r,"-panel-").concat(s),role:"tabpanel",tabIndex:i?0:-1,"aria-labelledby":r&&"".concat(r,"-tab-").concat(s),"aria-hidden":!i,style:c,className:l()(n,i&&"".concat(n,"-active"),o),ref:e},u)}),K=["renderTabBar"],q=["label","key"],F=function(t){var e=t.renderTabBar,n=(0,p.Z)(t,K),o=a.useContext(h).tabs;return e?e((0,d.Z)((0,d.Z)({},n),{},{panes:o.map(function(t){var e=t.label,n=t.key,o=(0,p.Z)(t,q);return a.createElement(H,(0,s.Z)({tab:e,key:n,tabKey:n},o))})}),X):a.createElement(X,n)},V=n(8070),Y=["key","forceRender","style","className","destroyInactiveTabPane"],Q=function(t){var e=t.id,n=t.activeKey,o=t.animated,c=t.tabPosition,r=t.destroyInactiveTabPane,i=a.useContext(h),f=i.prefixCls,v=i.tabs,b=o.tabPane,m="".concat(f,"-tabpane");return a.createElement("div",{className:l()("".concat(f,"-content-holder"))},a.createElement("div",{className:l()("".concat(f,"-content"),"".concat(f,"-content-").concat(c),(0,u.Z)({},"".concat(f,"-content-animated"),b))},v.map(function(t){var c=t.key,i=t.forceRender,u=t.style,f=t.className,v=t.destroyInactiveTabPane,h=(0,p.Z)(t,Y),g=c===n;return a.createElement(V.ZP,(0,s.Z)({key:c,visible:g,forceRender:i,removeOnLeave:!!(r||v),leavedClassName:"".concat(m,"-hidden")},o.tabPaneMotion),function(t,n){var o=t.style,r=t.className;return a.createElement(H,(0,s.Z)({},h,{prefixCls:m,id:e,tabKey:c,animated:b,active:g,style:(0,d.Z)((0,d.Z)({},u),o),className:l()(f,r),ref:n}))})})))};n(1700);var U=["id","prefixCls","className","items","direction","activeKey","defaultActiveKey","editable","animated","tabPosition","tabBarGutter","tabBarStyle","tabBarExtraContent","locale","more","destroyInactiveTabPane","renderTabBar","onChange","onTabClick","onTabScroll","getPopupContainer","popupClassName","indicator"],J=0,$=a.forwardRef(function(t,e){var n=t.id,o=t.prefixCls,c=void 0===o?"rc-tabs":o,r=t.className,i=t.items,g=t.direction,y=t.activeKey,k=t.defaultActiveKey,w=t.editable,x=t.animated,_=t.tabPosition,S=void 0===_?"top":_,C=t.tabBarGutter,E=t.tabBarStyle,Z=t.tabBarExtraContent,R=t.locale,P=t.more,T=t.destroyInactiveTabPane,M=t.renderTabBar,O=t.onChange,I=t.onTabClick,L=t.onTabScroll,N=t.getPopupContainer,D=t.popupClassName,B=t.indicator,z=(0,p.Z)(t,U),j=a.useMemo(function(){return(i||[]).filter(function(t){return t&&"object"===(0,v.Z)(t)&&"key"in t})},[i]),A="rtl"===g,W=function(){var t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{inkBar:!0,tabPane:!1};return(t=!1===e?{inkBar:!1,tabPane:!1}:!0===e?{inkBar:!0,tabPane:!1}:(0,d.Z)({inkBar:!0},"object"===(0,v.Z)(e)?e:{})).tabPaneMotion&&void 0===t.tabPane&&(t.tabPane=!0),!t.tabPaneMotion&&t.tabPane&&(t.tabPane=!1),t}(x),G=(0,a.useState)(!1),X=(0,f.Z)(G,2),H=X[0],K=X[1];(0,a.useEffect)(function(){K((0,m.Z)())},[]);var q=(0,b.Z)(function(){var t;return null===(t=j[0])||void 0===t?void 0:t.key},{value:y,defaultValue:k}),V=(0,f.Z)(q,2),Y=V[0],$=V[1],tt=(0,a.useState)(function(){return j.findIndex(function(t){return t.key===Y})}),te=(0,f.Z)(tt,2),tn=te[0],ta=te[1];(0,a.useEffect)(function(){var t,e=j.findIndex(function(t){return t.key===Y});-1===e&&(e=Math.max(0,Math.min(tn,j.length-1)),$(null===(t=j[e])||void 0===t?void 0:t.key)),ta(e)},[j.map(function(t){return t.key}).join("_"),Y,tn]);var to=(0,b.Z)(null,{value:n}),tc=(0,f.Z)(to,2),tr=tc[0],ti=tc[1];(0,a.useEffect)(function(){n||(ti("rc-tabs-".concat(J)),J+=1)},[]);var tl={id:tr,activeKey:Y,animated:W,tabPosition:S,rtl:A,mobile:H},ts=(0,d.Z)((0,d.Z)({},tl),{},{editable:w,locale:R,more:P,tabBarGutter:C,onTabClick:function(t,e){null==I||I(t,e);var n=t!==Y;$(t),n&&(null==O||O(t))},onTabScroll:L,extra:Z,style:E,panes:null,getPopupContainer:N,popupClassName:D,indicator:B});return a.createElement(h.Provider,{value:{tabs:j,prefixCls:c}},a.createElement("div",(0,s.Z)({ref:e,id:n,className:l()(c,"".concat(c,"-").concat(S),(0,u.Z)((0,u.Z)((0,u.Z)({},"".concat(c,"-mobile"),H),"".concat(c,"-editable"),w),"".concat(c,"-rtl"),A),r)},z),a.createElement(F,(0,s.Z)({},ts,{renderTabBar:M})),a.createElement(Q,(0,s.Z)({destroyInactiveTabPane:T},tl,{animated:W}))))}),tt=n(8539),te=n(3772),tn=n(9801),ta=n(169);let to={motionAppear:!1,motionEnter:!0,motionLeave:!0};var tc=n(5610),tr=function(t,e){var n={};for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&0>e.indexOf(a)&&(n[a]=t[a]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(t);o<a.length;o++)0>e.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(t,a[o])&&(n[a[o]]=t[a[o]]);return n},ti=n(7349),tl=n(5334),ts=n(4547),tu=n(4645),td=n(9355),tf=t=>{let{componentCls:e,motionDurationSlow:n}=t;return[{[e]:{["".concat(e,"-switch")]:{"&-appear, &-enter":{transition:"none","&-start":{opacity:0},"&-active":{opacity:1,transition:"opacity ".concat(n)}},"&-leave":{position:"absolute",transition:"none",inset:0,"&-start":{opacity:1},"&-active":{opacity:0,transition:"opacity ".concat(n)}}}}},[(0,td.oN)(t,"slide-up"),(0,td.oN)(t,"slide-down")]]};let tv=t=>{let{componentCls:e,tabsCardPadding:n,cardBg:a,cardGutter:o,colorBorderSecondary:c,itemSelectedColor:r}=t;return{["".concat(e,"-card")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab")]:{margin:0,padding:n,background:a,border:"".concat((0,ti.bf)(t.lineWidth)," ").concat(t.lineType," ").concat(c),transition:"all ".concat(t.motionDurationSlow," ").concat(t.motionEaseInOut)},["".concat(e,"-tab-active")]:{color:r,background:t.colorBgContainer},["".concat(e,"-tab-focus")]:Object.assign({},(0,tl.oN)(t,-3)),["".concat(e,"-ink-bar")]:{visibility:"hidden"},["& ".concat(e,"-tab").concat(e,"-tab-focus ").concat(e,"-tab-btn")]:{outline:"none"}},["&".concat(e,"-top, &").concat(e,"-bottom")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab + ").concat(e,"-tab")]:{marginLeft:{_skip_check_:!0,value:(0,ti.bf)(o)}}}},["&".concat(e,"-top")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab")]:{borderRadius:"".concat((0,ti.bf)(t.borderRadiusLG)," ").concat((0,ti.bf)(t.borderRadiusLG)," 0 0")},["".concat(e,"-tab-active")]:{borderBottomColor:t.colorBgContainer}}},["&".concat(e,"-bottom")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab")]:{borderRadius:"0 0 ".concat((0,ti.bf)(t.borderRadiusLG)," ").concat((0,ti.bf)(t.borderRadiusLG))},["".concat(e,"-tab-active")]:{borderTopColor:t.colorBgContainer}}},["&".concat(e,"-left, &").concat(e,"-right")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab + ").concat(e,"-tab")]:{marginTop:(0,ti.bf)(o)}}},["&".concat(e,"-left")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab")]:{borderRadius:{_skip_check_:!0,value:"".concat((0,ti.bf)(t.borderRadiusLG)," 0 0 ").concat((0,ti.bf)(t.borderRadiusLG))}},["".concat(e,"-tab-active")]:{borderRightColor:{_skip_check_:!0,value:t.colorBgContainer}}}},["&".concat(e,"-right")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab")]:{borderRadius:{_skip_check_:!0,value:"0 ".concat((0,ti.bf)(t.borderRadiusLG)," ").concat((0,ti.bf)(t.borderRadiusLG)," 0")}},["".concat(e,"-tab-active")]:{borderLeftColor:{_skip_check_:!0,value:t.colorBgContainer}}}}}}},tp=t=>{let{componentCls:e,itemHoverColor:n,dropdownEdgeChildVerticalPadding:a}=t;return{["".concat(e,"-dropdown")]:Object.assign(Object.assign({},(0,tl.Wf)(t)),{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:t.zIndexPopup,display:"block","&-hidden":{display:"none"},["".concat(e,"-dropdown-menu")]:{maxHeight:t.tabsDropdownHeight,margin:0,padding:"".concat((0,ti.bf)(a)," 0"),overflowX:"hidden",overflowY:"auto",textAlign:{_skip_check_:!0,value:"left"},listStyleType:"none",backgroundColor:t.colorBgContainer,backgroundClip:"padding-box",borderRadius:t.borderRadiusLG,outline:"none",boxShadow:t.boxShadowSecondary,"&-item":Object.assign(Object.assign({},tl.vS),{display:"flex",alignItems:"center",minWidth:t.tabsDropdownWidth,margin:0,padding:"".concat((0,ti.bf)(t.paddingXXS)," ").concat((0,ti.bf)(t.paddingSM)),color:t.colorText,fontWeight:"normal",fontSize:t.fontSize,lineHeight:t.lineHeight,cursor:"pointer",transition:"all ".concat(t.motionDurationSlow),"> span":{flex:1,whiteSpace:"nowrap"},"&-remove":{flex:"none",marginLeft:{_skip_check_:!0,value:t.marginSM},color:t.colorTextDescription,fontSize:t.fontSizeSM,background:"transparent",border:0,cursor:"pointer","&:hover":{color:n}},"&:hover":{background:t.controlItemBgHover},"&-disabled":{"&, &:hover":{color:t.colorTextDisabled,background:"transparent",cursor:"not-allowed"}}})}})}},tb=t=>{let{componentCls:e,margin:n,colorBorderSecondary:a,horizontalMargin:o,verticalItemPadding:c,verticalItemMargin:r,calc:i}=t;return{["".concat(e,"-top, ").concat(e,"-bottom")]:{flexDirection:"column",["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{margin:o,"&::before":{position:"absolute",right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},borderBottom:"".concat((0,ti.bf)(t.lineWidth)," ").concat(t.lineType," ").concat(a),content:"''"},["".concat(e,"-ink-bar")]:{height:t.lineWidthBold,"&-animated":{transition:"width ".concat(t.motionDurationSlow,", left ").concat(t.motionDurationSlow,",\n            right ").concat(t.motionDurationSlow)}},["".concat(e,"-nav-wrap")]:{"&::before, &::after":{top:0,bottom:0,width:t.controlHeight},"&::before":{left:{_skip_check_:!0,value:0},boxShadow:t.boxShadowTabsOverflowLeft},"&::after":{right:{_skip_check_:!0,value:0},boxShadow:t.boxShadowTabsOverflowRight},["&".concat(e,"-nav-wrap-ping-left::before")]:{opacity:1},["&".concat(e,"-nav-wrap-ping-right::after")]:{opacity:1}}}},["".concat(e,"-top")]:{["> ".concat(e,"-nav,\n        > div > ").concat(e,"-nav")]:{"&::before":{bottom:0},["".concat(e,"-ink-bar")]:{bottom:0}}},["".concat(e,"-bottom")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{order:1,marginTop:n,marginBottom:0,"&::before":{top:0},["".concat(e,"-ink-bar")]:{top:0}},["> ".concat(e,"-content-holder, > div > ").concat(e,"-content-holder")]:{order:0}},["".concat(e,"-left, ").concat(e,"-right")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{flexDirection:"column",minWidth:i(t.controlHeight).mul(1.25).equal(),["".concat(e,"-tab")]:{padding:c,textAlign:"center"},["".concat(e,"-tab + ").concat(e,"-tab")]:{margin:r},["".concat(e,"-nav-wrap")]:{flexDirection:"column","&::before, &::after":{right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},height:t.controlHeight},"&::before":{top:0,boxShadow:t.boxShadowTabsOverflowTop},"&::after":{bottom:0,boxShadow:t.boxShadowTabsOverflowBottom},["&".concat(e,"-nav-wrap-ping-top::before")]:{opacity:1},["&".concat(e,"-nav-wrap-ping-bottom::after")]:{opacity:1}},["".concat(e,"-ink-bar")]:{width:t.lineWidthBold,"&-animated":{transition:"height ".concat(t.motionDurationSlow,", top ").concat(t.motionDurationSlow)}},["".concat(e,"-nav-list, ").concat(e,"-nav-operations")]:{flex:"1 0 auto",flexDirection:"column"}}},["".concat(e,"-left")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-ink-bar")]:{right:{_skip_check_:!0,value:0}}},["> ".concat(e,"-content-holder, > div > ").concat(e,"-content-holder")]:{marginLeft:{_skip_check_:!0,value:(0,ti.bf)(i(t.lineWidth).mul(-1).equal())},borderLeft:{_skip_check_:!0,value:"".concat((0,ti.bf)(t.lineWidth)," ").concat(t.lineType," ").concat(t.colorBorder)},["> ".concat(e,"-content > ").concat(e,"-tabpane")]:{paddingLeft:{_skip_check_:!0,value:t.paddingLG}}}},["".concat(e,"-right")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{order:1,["".concat(e,"-ink-bar")]:{left:{_skip_check_:!0,value:0}}},["> ".concat(e,"-content-holder, > div > ").concat(e,"-content-holder")]:{order:0,marginRight:{_skip_check_:!0,value:i(t.lineWidth).mul(-1).equal()},borderRight:{_skip_check_:!0,value:"".concat((0,ti.bf)(t.lineWidth)," ").concat(t.lineType," ").concat(t.colorBorder)},["> ".concat(e,"-content > ").concat(e,"-tabpane")]:{paddingRight:{_skip_check_:!0,value:t.paddingLG}}}}}},tm=t=>{let{componentCls:e,cardPaddingSM:n,cardPaddingLG:a,horizontalItemPaddingSM:o,horizontalItemPaddingLG:c}=t;return{[e]:{"&-small":{["> ".concat(e,"-nav")]:{["".concat(e,"-tab")]:{padding:o,fontSize:t.titleFontSizeSM}}},"&-large":{["> ".concat(e,"-nav")]:{["".concat(e,"-tab")]:{padding:c,fontSize:t.titleFontSizeLG}}}},["".concat(e,"-card")]:{["&".concat(e,"-small")]:{["> ".concat(e,"-nav")]:{["".concat(e,"-tab")]:{padding:n}},["&".concat(e,"-bottom")]:{["> ".concat(e,"-nav ").concat(e,"-tab")]:{borderRadius:"0 0 ".concat((0,ti.bf)(t.borderRadius)," ").concat((0,ti.bf)(t.borderRadius))}},["&".concat(e,"-top")]:{["> ".concat(e,"-nav ").concat(e,"-tab")]:{borderRadius:"".concat((0,ti.bf)(t.borderRadius)," ").concat((0,ti.bf)(t.borderRadius)," 0 0")}},["&".concat(e,"-right")]:{["> ".concat(e,"-nav ").concat(e,"-tab")]:{borderRadius:{_skip_check_:!0,value:"0 ".concat((0,ti.bf)(t.borderRadius)," ").concat((0,ti.bf)(t.borderRadius)," 0")}}},["&".concat(e,"-left")]:{["> ".concat(e,"-nav ").concat(e,"-tab")]:{borderRadius:{_skip_check_:!0,value:"".concat((0,ti.bf)(t.borderRadius)," 0 0 ").concat((0,ti.bf)(t.borderRadius))}}}},["&".concat(e,"-large")]:{["> ".concat(e,"-nav")]:{["".concat(e,"-tab")]:{padding:a}}}}}},th=t=>{let{componentCls:e,itemActiveColor:n,itemHoverColor:a,iconCls:o,tabsHorizontalItemMargin:c,horizontalItemPadding:r,itemSelectedColor:i,itemColor:l}=t,s="".concat(e,"-tab");return{[s]:{position:"relative",WebkitTouchCallout:"none",WebkitTapHighlightColor:"transparent",display:"inline-flex",alignItems:"center",padding:r,fontSize:t.titleFontSize,background:"transparent",border:0,outline:"none",cursor:"pointer",color:l,"&-btn, &-remove":{"&:focus:not(:focus-visible), &:active":{color:n}},"&-btn":{outline:"none",transition:"all ".concat(t.motionDurationSlow),["".concat(s,"-icon:not(:last-child)")]:{marginInlineEnd:t.marginSM}},"&-remove":Object.assign({flex:"none",marginRight:{_skip_check_:!0,value:t.calc(t.marginXXS).mul(-1).equal()},marginLeft:{_skip_check_:!0,value:t.marginXS},color:t.colorTextDescription,fontSize:t.fontSizeSM,background:"transparent",border:"none",outline:"none",cursor:"pointer",transition:"all ".concat(t.motionDurationSlow),"&:hover":{color:t.colorTextHeading}},(0,tl.Qy)(t)),"&:hover":{color:a},["&".concat(s,"-active ").concat(s,"-btn")]:{color:i,textShadow:t.tabsActiveTextShadow},["&".concat(s,"-focus ").concat(s,"-btn")]:Object.assign({},(0,tl.oN)(t)),["&".concat(s,"-disabled")]:{color:t.colorTextDisabled,cursor:"not-allowed"},["&".concat(s,"-disabled ").concat(s,"-btn, &").concat(s,"-disabled ").concat(e,"-remove")]:{"&:focus, &:active":{color:t.colorTextDisabled}},["& ".concat(s,"-remove ").concat(o)]:{margin:0},["".concat(o,":not(:last-child)")]:{marginRight:{_skip_check_:!0,value:t.marginSM}}},["".concat(s," + ").concat(s)]:{margin:{_skip_check_:!0,value:c}}}},tg=t=>{let{componentCls:e,tabsHorizontalItemMarginRTL:n,iconCls:a,cardGutter:o,calc:c}=t;return{["".concat(e,"-rtl")]:{direction:"rtl",["".concat(e,"-nav")]:{["".concat(e,"-tab")]:{margin:{_skip_check_:!0,value:n},["".concat(e,"-tab:last-of-type")]:{marginLeft:{_skip_check_:!0,value:0}},[a]:{marginRight:{_skip_check_:!0,value:0},marginLeft:{_skip_check_:!0,value:(0,ti.bf)(t.marginSM)}},["".concat(e,"-tab-remove")]:{marginRight:{_skip_check_:!0,value:(0,ti.bf)(t.marginXS)},marginLeft:{_skip_check_:!0,value:(0,ti.bf)(c(t.marginXXS).mul(-1).equal())},[a]:{margin:0}}}},["&".concat(e,"-left")]:{["> ".concat(e,"-nav")]:{order:1},["> ".concat(e,"-content-holder")]:{order:0}},["&".concat(e,"-right")]:{["> ".concat(e,"-nav")]:{order:0},["> ".concat(e,"-content-holder")]:{order:1}},["&".concat(e,"-card").concat(e,"-top, &").concat(e,"-card").concat(e,"-bottom")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab + ").concat(e,"-tab")]:{marginRight:{_skip_check_:!0,value:o},marginLeft:{_skip_check_:!0,value:0}}}}},["".concat(e,"-dropdown-rtl")]:{direction:"rtl"},["".concat(e,"-menu-item")]:{["".concat(e,"-dropdown-rtl")]:{textAlign:{_skip_check_:!0,value:"right"}}}}},ty=t=>{let{componentCls:e,tabsCardPadding:n,cardHeight:a,cardGutter:o,itemHoverColor:c,itemActiveColor:r,colorBorderSecondary:i}=t;return{[e]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,tl.Wf)(t)),{display:"flex",["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{position:"relative",display:"flex",flex:"none",alignItems:"center",["".concat(e,"-nav-wrap")]:{position:"relative",display:"flex",flex:"auto",alignSelf:"stretch",overflow:"hidden",whiteSpace:"nowrap",transform:"translate(0)","&::before, &::after":{position:"absolute",zIndex:1,opacity:0,transition:"opacity ".concat(t.motionDurationSlow),content:"''",pointerEvents:"none"}},["".concat(e,"-nav-list")]:{position:"relative",display:"flex",transition:"opacity ".concat(t.motionDurationSlow)},["".concat(e,"-nav-operations")]:{display:"flex",alignSelf:"stretch"},["".concat(e,"-nav-operations-hidden")]:{position:"absolute",visibility:"hidden",pointerEvents:"none"},["".concat(e,"-nav-more")]:{position:"relative",padding:n,background:"transparent",border:0,color:t.colorText,"&::after":{position:"absolute",right:{_skip_check_:!0,value:0},bottom:0,left:{_skip_check_:!0,value:0},height:t.calc(t.controlHeightLG).div(8).equal(),transform:"translateY(100%)",content:"''"}},["".concat(e,"-nav-add")]:Object.assign({minWidth:a,marginLeft:{_skip_check_:!0,value:o},padding:(0,ti.bf)(t.paddingXS),background:"transparent",border:"".concat((0,ti.bf)(t.lineWidth)," ").concat(t.lineType," ").concat(i),borderRadius:"".concat((0,ti.bf)(t.borderRadiusLG)," ").concat((0,ti.bf)(t.borderRadiusLG)," 0 0"),outline:"none",cursor:"pointer",color:t.colorText,transition:"all ".concat(t.motionDurationSlow," ").concat(t.motionEaseInOut),"&:hover":{color:c},"&:active, &:focus:not(:focus-visible)":{color:r}},(0,tl.Qy)(t,-3))},["".concat(e,"-extra-content")]:{flex:"none"},["".concat(e,"-ink-bar")]:{position:"absolute",background:t.inkBarColor,pointerEvents:"none"}}),th(t)),{["".concat(e,"-content")]:{position:"relative",width:"100%"},["".concat(e,"-content-holder")]:{flex:"auto",minWidth:0,minHeight:0},["".concat(e,"-tabpane")]:Object.assign(Object.assign({},(0,tl.Qy)(t)),{"&-hidden":{display:"none"}})}),["".concat(e,"-centered")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-nav-wrap")]:{["&:not([class*='".concat(e,"-nav-wrap-ping']) > ").concat(e,"-nav-list")]:{margin:"auto"}}}}}};var tk=(0,ts.I$)("Tabs",t=>{let e=(0,tu.IX)(t,{tabsCardPadding:t.cardPadding,dropdownEdgeChildVerticalPadding:t.paddingXXS,tabsActiveTextShadow:"0 0 0.25px currentcolor",tabsDropdownHeight:200,tabsDropdownWidth:120,tabsHorizontalItemMargin:"0 0 0 ".concat((0,ti.bf)(t.horizontalItemGutter)),tabsHorizontalItemMarginRTL:"0 0 0 ".concat((0,ti.bf)(t.horizontalItemGutter))});return[tm(e),tg(e),tb(e),tp(e),tv(e),ty(e),tf(e)]},t=>{let e=t.controlHeightLG;return{zIndexPopup:t.zIndexPopupBase+50,cardBg:t.colorFillAlter,cardHeight:e,cardPadding:"".concat((e-Math.round(t.fontSize*t.lineHeight))/2-t.lineWidth,"px ").concat(t.padding,"px"),cardPaddingSM:"".concat(1.5*t.paddingXXS,"px ").concat(t.padding,"px"),cardPaddingLG:"".concat(t.paddingXS,"px ").concat(t.padding,"px ").concat(1.5*t.paddingXXS,"px"),titleFontSize:t.fontSize,titleFontSizeLG:t.fontSizeLG,titleFontSizeSM:t.fontSize,inkBarColor:t.colorPrimary,horizontalMargin:"0 0 ".concat(t.margin,"px 0"),horizontalItemGutter:32,horizontalItemMargin:"",horizontalItemMarginRTL:"",horizontalItemPadding:"".concat(t.paddingSM,"px 0"),horizontalItemPaddingSM:"".concat(t.paddingXS,"px 0"),horizontalItemPaddingLG:"".concat(t.padding,"px 0"),verticalItemPadding:"".concat(t.paddingXS,"px ").concat(t.paddingLG,"px"),verticalItemMargin:"".concat(t.margin,"px 0 0 0"),itemColor:t.colorText,itemSelectedColor:t.colorPrimary,itemHoverColor:t.colorPrimaryHover,itemActiveColor:t.colorPrimaryActive,cardGutter:t.marginXXS/2}}),tw=n(4326),tx=function(t,e){var n={};for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&0>e.indexOf(a)&&(n[a]=t[a]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(t);o<a.length;o++)0>e.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(t,a[o])&&(n[a[o]]=t[a[o]]);return n};let t_=t=>{var e,n,i,s,u,d,f,v,p,b,m;let h;let{type:g,className:y,rootClassName:k,size:w,onEdit:x,hideAdd:_,centered:S,addIcon:C,removeIcon:E,moreIcon:Z,more:R,popupClassName:P,children:T,items:M,animated:O,style:I,indicatorSize:L,indicator:N}=t,D=tx(t,["type","className","rootClassName","size","onEdit","hideAdd","centered","addIcon","removeIcon","moreIcon","more","popupClassName","children","items","animated","style","indicatorSize","indicator"]),{prefixCls:B}=D,{direction:z,tabs:j,getPrefixCls:A,getPopupContainer:W}=a.useContext(tt.E_),G=A("tabs",B),X=(0,te.Z)(G),[H,K,q]=tk(G,X);"editable-card"===g&&(h={onEdit:(t,e)=>{let{key:n,event:a}=e;null==x||x("add"===t?a:n,t)},removeIcon:null!==(e=null!=E?E:null==j?void 0:j.removeIcon)&&void 0!==e?e:a.createElement(o.Z,null),addIcon:(null!=C?C:null==j?void 0:j.addIcon)||a.createElement(r.Z,null),showAdd:!0!==_});let F=A(),V=(0,tn.Z)(w),Y=M||(0,tc.Z)(T).map(t=>{if(a.isValidElement(t)){let{key:e,props:n}=t,a=n||{},{tab:o}=a,c=tr(a,["tab"]);return Object.assign(Object.assign({key:String(e)},c),{label:o})}return null}).filter(t=>t),Q=function(t){let e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{inkBar:!0,tabPane:!1};return(e=!1===n?{inkBar:!1,tabPane:!1}:!0===n?{inkBar:!0,tabPane:!0}:Object.assign({inkBar:!0},"object"==typeof n?n:{})).tabPane&&(e.tabPaneMotion=Object.assign(Object.assign({},to),{motionName:(0,ta.m)(t,"switch")})),e}(G,O),U=Object.assign(Object.assign({},null==j?void 0:j.style),I),J={align:null!==(n=null==N?void 0:N.align)&&void 0!==n?n:null===(i=null==j?void 0:j.indicator)||void 0===i?void 0:i.align,size:null!==(f=null!==(u=null!==(s=null==N?void 0:N.size)&&void 0!==s?s:L)&&void 0!==u?u:null===(d=null==j?void 0:j.indicator)||void 0===d?void 0:d.size)&&void 0!==f?f:null==j?void 0:j.indicatorSize};return H(a.createElement($,Object.assign({direction:z,getPopupContainer:W},D,{items:Y,className:l()({["".concat(G,"-").concat(V)]:V,["".concat(G,"-card")]:["card","editable-card"].includes(g),["".concat(G,"-editable-card")]:"editable-card"===g,["".concat(G,"-centered")]:S},null==j?void 0:j.className,y,k,K,q,X),popupClassName:l()(P,K,q,X),style:U,editable:h,more:Object.assign({icon:null!==(m=null!==(b=null!==(p=null===(v=null==j?void 0:j.more)||void 0===v?void 0:v.icon)&&void 0!==p?p:null==j?void 0:j.moreIcon)&&void 0!==b?b:Z)&&void 0!==m?m:a.createElement(c.Z,null),transitionName:"".concat(F,"-slide-up")},R),prefixCls:G,animated:Q,indicator:J})))};t_.TabPane=tw.Z;var tS=t_},8698:function(t,e,n){n.d(e,{Z:function(){return w}});var a=n(5773),o=n(4649),c=n(8136),r=n(6535),i=n(1777),l=n(5),s=n.n(l),u=n(7861),d=n(7378),f=n(7237),v=n(4978),p=f.Z.ESC,b=f.Z.TAB,m=(0,d.forwardRef)(function(t,e){var n=t.overlay,a=t.arrow,o=t.prefixCls,c=(0,d.useMemo)(function(){return"function"==typeof n?n():n},[n]),r=(0,u.sQ)(e,(0,u.C4)(c));return d.createElement(d.Fragment,null,a&&d.createElement("div",{className:"".concat(o,"-arrow")}),d.cloneElement(c,{ref:(0,u.Yr)(c)?r:void 0}))}),h={adjustX:1,adjustY:1},g=[0,0],y={topLeft:{points:["bl","tl"],overflow:h,offset:[0,-4],targetOffset:g},top:{points:["bc","tc"],overflow:h,offset:[0,-4],targetOffset:g},topRight:{points:["br","tr"],overflow:h,offset:[0,-4],targetOffset:g},bottomLeft:{points:["tl","bl"],overflow:h,offset:[0,4],targetOffset:g},bottom:{points:["tc","bc"],overflow:h,offset:[0,4],targetOffset:g},bottomRight:{points:["tr","br"],overflow:h,offset:[0,4],targetOffset:g}},k=["arrow","prefixCls","transitionName","animation","align","placement","placements","getPopupContainer","showAction","hideAction","overlayClassName","overlayStyle","visible","trigger","autoFocus","overlay","children","onVisibleChange"],w=d.forwardRef(function(t,e){var n,l,f,h,g,w,x,_,S,C,E,Z,R,P,T=t.arrow,M=void 0!==T&&T,O=t.prefixCls,I=void 0===O?"rc-dropdown":O,L=t.transitionName,N=t.animation,D=t.align,B=t.placement,z=t.placements,j=t.getPopupContainer,A=t.showAction,W=t.hideAction,G=t.overlayClassName,X=t.overlayStyle,H=t.visible,K=t.trigger,q=void 0===K?["hover"]:K,F=t.autoFocus,V=t.overlay,Y=t.children,Q=t.onVisibleChange,U=(0,r.Z)(t,k),J=d.useState(),$=(0,c.Z)(J,2),tt=$[0],te=$[1],tn="visible"in t?H:tt,ta=d.useRef(null),to=d.useRef(null),tc=d.useRef(null);d.useImperativeHandle(e,function(){return ta.current});var tr=function(t){te(t),null==Q||Q(t)};l=(n={visible:tn,triggerRef:tc,onVisibleChange:tr,autoFocus:F,overlayRef:to}).visible,f=n.triggerRef,h=n.onVisibleChange,g=n.autoFocus,w=n.overlayRef,x=d.useRef(!1),_=function(){if(l){var t,e;null===(t=f.current)||void 0===t||null===(e=t.focus)||void 0===e||e.call(t),null==h||h(!1)}},S=function(){var t;return null!==(t=w.current)&&void 0!==t&&!!t.focus&&(w.current.focus(),x.current=!0,!0)},C=function(t){switch(t.keyCode){case p:_();break;case b:var e=!1;x.current||(e=S()),e?t.preventDefault():_()}},d.useEffect(function(){return l?(window.addEventListener("keydown",C),g&&(0,v.Z)(S,3),function(){window.removeEventListener("keydown",C),x.current=!1}):function(){x.current=!1}},[l]);var ti=function(){return d.createElement(m,{ref:to,overlay:V,prefixCls:I,arrow:M})},tl=d.cloneElement(Y,{className:s()(null===(P=Y.props)||void 0===P?void 0:P.className,tn&&(void 0!==(E=t.openClassName)?E:"".concat(I,"-open"))),ref:(0,u.Yr)(Y)?(0,u.sQ)(tc,(0,u.C4)(Y)):void 0}),ts=W;return ts||-1===q.indexOf("contextMenu")||(ts=["click"]),d.createElement(i.Z,(0,a.Z)({builtinPlacements:void 0===z?y:z},U,{prefixCls:I,ref:ta,popupClassName:s()(G,(0,o.Z)({},"".concat(I,"-show-arrow"),M)),popupStyle:X,action:q,showAction:A,hideAction:ts,popupPlacement:void 0===B?"bottomLeft":B,popupAlign:D,popupTransitionName:L,popupAnimation:N,popupVisible:tn,stretch:(Z=t.minOverlayWidthMatchTrigger,R=t.alignPoint,"minOverlayWidthMatchTrigger"in t?Z:!R)?"minWidth":"",popup:"function"==typeof V?ti:ti(),onPopupVisibleChange:tr,onPopupClick:function(e){var n=t.onOverlayClick;te(!1),n&&n(e)},getPopupContainer:j}),tl)})}}]);