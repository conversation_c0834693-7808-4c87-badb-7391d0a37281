(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[299],{6086:function(e,t,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/vietplants/control",function(){return n(5569)}])},9731:function(e,t,n){"use strict";n.d(t,{Z:function(){return o}});var r=n(5773),i=n(7378),l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"}}]},name:"down",theme:"outlined"},c=n(3359),o=i.forwardRef(function(e,t){return i.createElement(c.Z,(0,r.Z)({},e,{ref:t,icon:l}))})},5605:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});var r=n(7378);function i(){let[,e]=r.useReducer(e=>e+1,0);return e}},8032:function(e,t,n){"use strict";n.d(t,{c4:function(){return l},m9:function(){return a}});var r=n(7378),i=n(1523);let l=["xxl","xl","lg","md","sm","xs"],c=e=>({xs:"(max-width: ".concat(e.screenXSMax,"px)"),sm:"(min-width: ".concat(e.screenSM,"px)"),md:"(min-width: ".concat(e.screenMD,"px)"),lg:"(min-width: ".concat(e.screenLG,"px)"),xl:"(min-width: ".concat(e.screenXL,"px)"),xxl:"(min-width: ".concat(e.screenXXL,"px)")}),o=e=>{let t=[].concat(l).reverse();return t.forEach((n,r)=>{let i=n.toUpperCase(),l="screen".concat(i,"Min"),c="screen".concat(i);if(!(e[l]<=e[c]))throw Error("".concat(l,"<=").concat(c," fails : !(").concat(e[l],"<=").concat(e[c],")"));if(r<t.length-1){let n="screen".concat(i,"Max");if(!(e[c]<=e[n]))throw Error("".concat(c,"<=").concat(n," fails : !(").concat(e[c],"<=").concat(e[n],")"));let l=t[r+1].toUpperCase(),o="screen".concat(l,"Min");if(!(e[n]<=e[o]))throw Error("".concat(n,"<=").concat(o," fails : !(").concat(e[n],"<=").concat(e[o],")"))}}),e},a=(e,t)=>{if(t){for(let n of l)if(e[n]&&(null==t?void 0:t[n])!==void 0)return t[n]}};t.ZP=()=>{let[,e]=(0,i.ZP)(),t=c(o(e));return r.useMemo(()=>{let e=new Map,n=-1,r={};return{responsiveMap:t,matchHandlers:{},dispatch:t=>(r=t,e.forEach(e=>e(r)),e.size>=1),subscribe(t){return e.size||this.register(),n+=1,e.set(n,t),t(r),n},unsubscribe(t){e.delete(t),e.size||this.unregister()},register(){Object.keys(t).forEach(e=>{let n=t[e],i=t=>{let{matches:n}=t;this.dispatch(Object.assign(Object.assign({},r),{[e]:n}))},l=window.matchMedia(n);l.addListener(i),this.matchHandlers[n]={mql:l,listener:i},i(l)})},unregister(){Object.keys(t).forEach(e=>{let n=t[e],r=this.matchHandlers[n];null==r||r.mql.removeListener(null==r?void 0:r.listener)}),e.clear()}}},[e])}},4735:function(e,t,n){"use strict";var r=n(7378),i=n(4812),l=n(5605),c=n(8032);t.Z=function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=(0,r.useRef)(t),o=(0,l.Z)(),a=(0,c.ZP)();return(0,i.Z)(()=>{let t=a.subscribe(t=>{n.current=t,e&&o()});return()=>a.unsubscribe(t)},[]),n.current}},483:function(e,t,n){"use strict";var r=n(4246),i=n(6502),l=n(8198);t.Z=e=>{let{functionItem:t}=e;return"Bool"===t.data_type?(0,r.jsx)(i.Z,{functionItem:t}):"Value"===t.data_type?(0,r.jsx)(l.Z,{functionItem:t}):void 0}},5569:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return s}});var r=n(4246),i=n(7378),l=n(9739),c=n(4456),o=n(4326),a=n(483);function s(){let{functionListForControl:e}=(0,l.Z)(),[t,n]=(0,i.useState)(null),[s,d]=(0,i.useState)(null);return(0,i.useEffect)(()=>{console.log("functionListForControl",e)},[e]),(0,r.jsxs)("div",{style:{display:"flex",flexDirection:"column",padding:16},children:[(0,r.jsx)("p",{style:{fontSize:24,fontWeight:"bold"},children:"Danh s\xe1ch điều khiển"}),(0,r.jsxs)("div",{style:{display:"flex",flexDirection:"row"},children:[(0,r.jsx)(c.Z,{defaultActiveKey:"1",tabPosition:"top",tabBarGutter:0,tabBarStyle:{overflow:"scroll"},style:{height:"440px",overflow:"scroll",width:"35%",paddingRight:16,borderRight:"1px solid #ddd"},children:e.map(e=>{var i,l,c;return(null==e?void 0:null===(i=e.children)||void 0===i?void 0:i.length)===0||(null==e?void 0:null===(l=e.identifier)||void 0===l?void 0:l.includes("rotect"))||(null==e?void 0:null===(c=e.identifier)||void 0===c?void 0:c.includes("onfig"))?null:(0,r.jsx)(o.Z,{tab:(0,r.jsx)("div",{style:{backgroundColor:"#fff",padding:8,border:"1px solid #ddd",borderRadius:8,marginRight:16,textAlign:"left",background:"rgb(183, 255, 203)"},children:e.label}),children:e.children.map(e=>{var i;return(null==e?void 0:null===(i=e.children)||void 0===i?void 0:i.length)===0?null:(0,r.jsx)("div",{style:{marginBottom:16,padding:8,border:"1px solid #ddd",borderRadius:8,background:t===e.label?"#45c3a1":"linear-gradient(to right,rgba(200,200,200,0.2),#fff)",color:t===e.label?"#fff":"#000",cursor:"pointer"},onClick:()=>{n(t===e.label?null:e.label),d(e.label)},children:(0,r.jsx)("p",{style:{fontWeight:"bold",margin:0},children:e.label})},e.label)})},e.label)})}),t&&(0,r.jsx)("div",{style:{flex:1,marginLeft:16},children:(0,r.jsx)(c.Z,{activeKey:s,onChange:e=>d(e),tabPosition:"top",tabBarGutter:0,tabBarStyle:{overflow:"scroll"},style:{height:"440px"},children:e.flatMap(e=>e.children).filter(e=>e.label===t).map(e=>(0,r.jsx)(o.Z,{tab:e.label,children:(0,r.jsx)("div",{style:{display:"flex",flexDirection:"column",gap:8,overflow:"scroll",height:"360px"},children:e.children.map(e=>(0,r.jsx)(a.Z,{functionItem:e},e.name))})},e.label))})})]})]})}}},function(e){e.O(0,[478,452,872,456,911,888,774,179],function(){return e(e.s=6086)}),_N_E=e.O()}]);