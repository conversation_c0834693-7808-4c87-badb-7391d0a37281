(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[16],{7301:function(l,e,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/vietplants/setting",function(){return t(9524)}])},483:function(l,e,t){"use strict";var i=t(4246),n=t(6502),d=t(8198);e.Z=l=>{let{functionItem:e}=l;return"Bool"===e.data_type?(0,i.jsx)(n.Z,{functionItem:e}):"Value"===e.data_type?(0,i.jsx)(d.Z,{functionItem:e}):void 0}},9524:function(l,e,t){"use strict";t.r(e),t.d(e,{default:function(){return g}});var i=t(4246),n=t(7378),d=t(9563),r=t(4478),o=t(4899),a=t(4456),s=t(9739),u=t(4326),c=t(483);let{Header:f,Content:h}=d.default,{Item:b}=r.default,{Option:p}=o.default;function g(){let{functionListForControl:l}=(0,s.Z)(),[e,t]=(0,n.useState)(null),[d,r]=(0,n.useState)(null);return(0,i.jsxs)("div",{style:{display:"flex",flexDirection:"column",padding:16},children:[(0,i.jsx)("p",{style:{fontSize:24,fontWeight:"bold"},children:"Danh s\xe1ch c\xe0i đặt"}),(0,i.jsxs)("div",{style:{display:"flex",flexDirection:"row"},children:[(0,i.jsx)(a.Z,{defaultActiveKey:"1",tabPosition:"top",tabBarGutter:0,tabBarStyle:{overflow:"scroll"},style:{height:"440px",overflow:"scroll",width:"35%",paddingRight:16,borderRight:"1px solid #ddd"},children:l.map(l=>{var n,d,o;return(null==l?void 0:null===(n=l.children)||void 0===n?void 0:n.length)!==0&&((null==l?void 0:null===(d=l.identifier)||void 0===d?void 0:d.includes("rotect"))||(null==l?void 0:null===(o=l.identifier)||void 0===o?void 0:o.includes("onfig")))?(0,i.jsx)(u.Z,{tab:(0,i.jsx)("div",{style:{backgroundColor:"#fff",padding:8,border:"1px solid #ddd",borderRadius:8,marginRight:16,textAlign:"left",background:"rgb(183, 255, 203)"},children:l.label}),children:l.children.map(l=>{var n;return(null==l?void 0:null===(n=l.children)||void 0===n?void 0:n.length)===0?null:(0,i.jsx)("div",{style:{marginBottom:16,padding:8,border:"1px solid #ddd",borderRadius:8,background:e===l.label?"#45c3a1":"linear-gradient(to right,rgba(200,200,200,0.2),#fff)",color:e===l.label?"#fff":"#000",cursor:"pointer"},onClick:()=>{t(e===l.label?null:l.label),r(l.label)},children:(0,i.jsx)("p",{style:{fontWeight:"bold",margin:0},children:l.label})},l.label)})},l.label):null})}),e&&(0,i.jsx)("div",{style:{flex:1,marginLeft:16},children:(0,i.jsx)(a.Z,{activeKey:d,onChange:l=>r(l),tabPosition:"top",tabBarGutter:0,tabBarStyle:{overflow:"scroll"},style:{height:"440px"},children:l.flatMap(l=>l.children).filter(l=>l.label===e).map(l=>(0,i.jsx)(u.Z,{tab:l.label,children:(0,i.jsx)("div",{style:{display:"flex",flexDirection:"column",gap:8,overflow:"scroll",height:"360px"},children:l.children.map(l=>(0,i.jsx)(c.Z,{functionItem:l},l.name))})},l.label))})})]})]})}}},function(l){l.O(0,[467,899,478,452,872,456,911,888,774,179],function(){return l(l.s=7301)}),_N_E=l.O()}]);