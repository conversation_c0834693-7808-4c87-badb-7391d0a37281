(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[515],{1746:function(e,t,l){(window.__NEXT_P=window.__NEXT_P||[]).push(["/vietplants/home",function(){return l(2760)}])},6502:function(e,t,l){"use strict";var i=l(4246),n=l(7378),o=l(4717),a=l(2754),r=l(2450),s=l(5032),d=l(1584),c=l(9739),u=l(1421),v=l(1624),f=l(9960);let h={width:"100%",display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"space-between",gap:8,border:"1px solid rgb(230,230,230)",padding:8,borderRadius:8,backgroundColor:"#fff"};t.Z=e=>{let{functionItem:t,readonly:l,setResponseStatusOfOnOffControl:g}=e,{languageData:x}=(0,o.Z)(),{deviceData:p,deviceId:y}=(0,c.Z)(),{data:m,loading:j,run:_}=(0,d.Z)(),[b,w]=(0,n.useState)(!1),{run:S,loading:T}=(0,u.Z)(),{handleMessage:C,client:E}=(0,v.T)(),[Z,F]=(0,n.useState)(!1),k=e=>"true"===String(e);function N(){p.name&&t.identifier&&(console.log("Start to get new data"),_({deviceId:null==p?void 0:p.name,keys:[t.identifier]}))}(0,n.useEffect)(()=>{N()},[null==p?void 0:p.name,t.identifier]),(0,n.useEffect)(()=>{var e;if(!(null==p?void 0:p.name)||!t.identifier)return;let l=(null==m?void 0:null===(e=m.data)||void 0===e?void 0:e[null==t?void 0:t.identifier])||[];if(console.log("latestDataWithFunctionKey",l),l.length>0){let e=l[l.length-1];e&&F(k(null==e?void 0:e.value))}(null==m?void 0:m.data)&&w(!0)},[m,null==p?void 0:p.name,t.identifier]),(0,n.useEffect)(()=>{b&&E.on("message",(e,l)=>{if(e===(0,f.v)(y)){console.log("ON OFF CONTROL: Received MQTT message on topic ".concat(e,":"),l.toString());try{let e=JSON.parse(l.toString());if(Array.isArray(e)){let l=e.filter(e=>e.key===t.identifier);if(l.length>0){let e=l[l.length-1];e&&F(k(null==e?void 0:e.value))}}}catch(e){console.error("MQTT message error:",e)}}})},[b,C,null==p?void 0:p.name,t.identifier]);let I=async e=>{let l=null==p?void 0:p.name;l&&t.identifier&&(await S({device_id_thingsboard:l,method:"set_state",params:{[t.identifier]:e}}),N())};return(0,n.useEffect)(()=>{g&&g(Z)},[Z]),(0,i.jsxs)("div",{style:h,children:[(0,i.jsxs)("div",{style:{display:"flex",flexDirection:"row",alignItems:"center",gap:8},children:[t.icon_url?(0,i.jsx)("img",{height:"24px",src:(0,a.rH)("api/v2/file/download?file_url="+t.icon_url),onError:()=>(0,i.jsx)(r.Z,{})}):(0,i.jsx)(r.Z,{}),(0,i.jsx)("p",{style:{margin:0,color:"rgb(100,100,100)"},children:t.label})]}),(0,i.jsx)(s.Z,{disabled:l,value:Z,onChange:I,checked:Z,loading:T||j,checkedChildren:t.data_on_text?t.data_on_text:x["common.control.switch.on"],unCheckedChildren:t.data_off_text?t.data_off_text:x["common.control.switch.off"],style:{minWidth:"60px"}})]})}},2760:function(e,t,l){"use strict";l.r(t),l.d(t,{default:function(){return k}});var i=l(4246),n=l(3992),o=l(1671),a=l(7378),r=l(1624),s=l(9739),d=l(9341),c=l(4210),u=l(3382),v=l(9960),f=e=>{let{deviceKey:t,dataFunction:l}=e,n=(null==l?void 0:l.data_measure_min)||0,o=(null==l?void 0:l.data_measure_max)||100,[c,f]=(0,a.useState)(0),{deviceId:h}=(0,s.Z)(),{subscribe:g,unsubscribe:x,handleMessage:p,client:y}=(0,r.T)(),{deviceData:m}=(0,s.Z)();return(0,a.useEffect)(()=>{h&&(null==l?void 0:l.identifier)&&(e(),y.on("message",(e,t)=>{if(e===(0,v.v)(h)){console.log("GAUGE CHART: Received MQTT message on topic ".concat(e,":"),t.toString());try{let e=JSON.parse(t.toString());if(Array.isArray(e)){let t=e.filter(e=>e.key===l.identifier);if(t.length>0){let e=t[t.length-1],l=parseFloat(String(e.value));f(isNaN(l)?0:parseFloat(l.toFixed(2)))}}}catch(e){console.error("MQTT message error:",e)}}}));async function e(){var e,t;let i=await (0,d.$U)({deviceId:h,keys:[l.identifier]}),n=null==i?void 0:null===(t=i.data)||void 0===t?void 0:null===(e=t[l.identifier])||void 0===e?void 0:e[0];if(n){let e=parseFloat(n.value);f(isNaN(e)?0:parseFloat(e.toFixed(2)))}}},[h,null==l?void 0:l.identifier]),(0,i.jsxs)("div",{style:{display:"flex",flexDirection:"row",alignItems:"flex-end",justifyContent:"center"},children:[(0,i.jsx)("p",{style:{color:"gray"},children:n}),(0,i.jsxs)("div",{style:{display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center"},children:[(0,i.jsx)("p",{style:{fontSize:"16px",fontWeight:"bold"},children:(null==l?void 0:l.label)||"Untitled"}),(0,i.jsx)(u.Z,{type:"dashboard",steps:48,percent:(c-n)/(o-n)*100,trailColor:"rgba(0, 0, 0, 0.06)",strokeWidth:10,format:e=>(0,i.jsxs)("div",{style:{color:(c-n)/(o-n)*100<50?"#45c3a1":(c-n)/(o-n)*100<75?"#FF8C00":"red"},children:[(0,i.jsx)("p",{style:{fontSize:"16px",fontWeight:"bold"},children:c}),(0,i.jsx)("p",{style:{fontSize:"14px",margin:0},children:null==l?void 0:l.unit})]}),strokeColor:(c-n)/(o-n)*100<50?"#45c3a1":(c-n)/(o-n)*100<75?"#FF8C00":"red",status:"active"})]}),(0,i.jsx)("p",{style:{color:"gray"},children:o})]})},h=l(4836);l(489);var g=l(213),x=l(6316),p=l(7693),y=l.n(p),m=l(805),j=l(8211);let _=(0,m.U)((0,j.n)((e,t)=>({ec:"0",ph:"0",temp:"0",setEc:t=>e({ec:t}),setPh:t=>e({ph:t}),setTemp:t=>e({temp:t})})));var b=e=>{let{dataFunction:t,lineColor:l="#45c3a1"}=e,{setEc:n,setPh:o,setTemp:r}=_(),{deviceId:c}=(0,s.Z)(),u=(0,a.useRef)(null),[v,f]=(0,a.useState)("0"),[p,m]=(0,a.useState)(null),[j,b]=(0,a.useState)([[],[]]),w=y()().startOf("day"),S=w.subtract(1,"day"),[T,C]=(0,a.useState)(S),[E,Z]=(0,a.useState)(w),[F,k]=(0,a.useState)(!1);(0,a.useEffect)(()=>{if(console.log("startDateTime",T),console.log("endDateTime",E),console.log("dataFunction",t),!T||!E||!c||!(null==t?void 0:t.identifier))return;let e=T.valueOf(),l=E.valueOf();if(l<=e)return;let{interval:i,agg:n,limit:o}=function(e,t){let l=t-e,i=6e4*Math.ceil(l/200/6e4);return{agg:"AVG",interval:i,limit:Math.ceil(l/i)}}(e,l);!async function(){let a=await (0,d.gF)({keys:t.identifier,startTs:e,endTs:l,agg:n,device_id_thingsboard:c,limit:o,interval:i}),r=(null==a?void 0:a[t.identifier])||[],s=[],u=[];r.forEach(e=>{s.push(e.ts/1e3),u.push(parseFloat(e.value))}),b([s,u])}()},[F,null==T?void 0:T.valueOf(),null==E?void 0:E.valueOf(),null==t?void 0:t.identifier,c]);let N="".concat((null==t?void 0:t.label)||"Untitled"," (").concat((null==t?void 0:t.unit)||"",")");return(0,a.useEffect)(()=>{if(!u.current)return;p&&p.destroy();let e={width:u.current.offsetWidth||600,height:150,scales:{x:{time:!0},y:{auto:!0}},series:[{},{label:N,stroke:l,width:2}],axes:[{stroke:"#999"},{stroke:"#999"}]},t=new h.Z(e,j,u.current);m(t);let i=()=>{t.destroy(),m(new h.Z(e,j,u.current))};return window.addEventListener("resize",i),()=>{t.destroy(),window.removeEventListener("resize",i)}},[j,N,l]),(0,i.jsxs)("div",{style:{display:"flex",flexDirection:"column"},children:[(0,i.jsxs)("div",{style:{display:"flex",gap:8,marginBottom:8},children:[(0,i.jsx)(g.default,{showTime:!0,placeholder:"Thời gian bắt đầu",value:T,onChange:e=>{!e||T&&e.isSame(T)||C(e)},style:{flex:1}}),(0,i.jsx)(g.default,{showTime:!0,placeholder:"Thời gian kết th\xfac",value:E,onChange:e=>{!e||E&&e.isSame(E)||Z(e)},style:{flex:1}}),(0,i.jsx)(x.ZP,{onClick:()=>k(Date.now()),children:"OK"})]}),(0,i.jsx)("div",{ref:u,className:"chart"})]})},w=l(2754),S=l(2450),T=e=>{let{dataFunction:t,deviceKey:l}=e,[n,o]=(0,a.useState)(0),{deviceId:c,deviceData:u}=(0,s.Z)(),{subscribe:f,unsubscribe:h,handleMessage:g,client:x}=(0,r.T)();return(0,a.useEffect)(()=>{async function e(){var e,l;let i=await (0,d.$U)({deviceId:c,keys:[t.identifier]}),n=null==i?void 0:null===(l=i.data)||void 0===l?void 0:null===(e=l[t.identifier])||void 0===e?void 0:e[0];if(n){let e=parseFloat(n.value);o(isNaN(e)?"":e.toFixed(2))}}console.log("deviceKey:",l),console.log("deviceId:",c),console.log("dataFunction:",t),c&&(null==t?void 0:t.identifier)&&(e(),x.on("message",(e,l)=>{if(e===(0,v.v)(c)){console.log("UN CHART: Received MQTT message on topic ".concat(e,":"),l.toString());try{let e=JSON.parse(l.toString());if(Array.isArray(e)){let l=e.filter(e=>e.key===t.identifier);if(l.length>0){let e=l[l.length-1],t=parseFloat(String(e.value));o(isNaN(t)?"":t.toFixed(2))}}}catch(e){console.error("MQTT message error:",e)}}}))},[c,null==t?void 0:t.identifier]),(0,i.jsxs)("div",{style:{width:"100%",display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"space-between",gap:8,border:"1px solid rgb(230,230,230)",padding:8,borderRadius:8,backgroundColor:"#fff"},children:[(0,i.jsxs)("div",{style:{display:"flex",flexDirection:"row",alignItems:"center",gap:8},children:[(null==t?void 0:t.icon_url)?(0,i.jsx)("img",{height:"24px",src:(0,w.rH)("api/v2/file/download?file_url="+t.icon_url),onError:()=>(0,i.jsx)(S.Z,{})}):(0,i.jsx)(S.Z,{}),(0,i.jsx)("p",{style:{margin:0,color:"rgb(100,100,100)"},children:null==t?void 0:t.label})]}),(0,i.jsxs)("p",{style:{fontWeight:"bold",fontSize:"16px",margin:0},children:[n," ",null==t?void 0:t.unit]})]})};let C={Gauge:e=>(0,i.jsx)(f,{...e}),Line:e=>(0,i.jsx)(b,{...e}),Unchart:e=>(0,i.jsx)(T,{...e})};var E=e=>{var t,l;let n=null===(t=e.dataFunction)||void 0===t?void 0:t.chart_type,[o,r]=(0,a.useState)("Gauge"===n||"Line"===n?n:"Unchart");(0,a.useEffect)(()=>{var t;let l=null===(t=e.dataFunction)||void 0===t?void 0:t.chart_type;"Gauge"===l||"Line"===l?r(l):r("Unchart")},[null===(l=e.dataFunction)||void 0===l?void 0:l.chart_type]);let s=(0,a.useMemo)(()=>C[o],[o]);return"Unchart"===o?(0,i.jsx)(T,{...e}):(0,i.jsxs)("div",{style:{padding:16},children:[(0,i.jsx)(c.Z,{options:["Line","Gauge"],value:o,onChange:e=>{r(e)},style:{marginBottom:16}}),(0,i.jsx)(s,{...e})]})},Z=e=>{let{functionItem:t,lineColor:l}=e;return(0,i.jsx)("div",{style:{boxShadow:"0px 1px 10px -5px rgba(0, 0, 0, 0.1)",backgroundColor:"#fff",borderRadius:8},children:t.show_chart?(0,i.jsx)(E,{dataFunction:t,deviceId:t.device_id_thingsboard,deviceKey:t.identifier,lineColor:l}):(0,i.jsx)(i.Fragment,{})})},F=l(6502);function k(){let{functionListForMonitor:e,deviceId:t}=(0,s.Z)();(0,a.useEffect)(()=>{console.log(e)},[e]);let[l,c]=(0,a.useState)(!1);(0,a.useEffect)(()=>{console.log(e),l||(e.map(e=>(e.children.sort((e,t)=>{let l=e.children.some(e=>"Bool"===e.data_type),i=t.children.some(e=>"Bool"===e.data_type);return l&&!i?-1:!l&&i?1:0}),e)),c(!0))},[e]);let[u,v]=(0,a.useState)({"valve-1":!1,"valve-2":!1,"valve-3":!1,"valve-4":!1,"valve-5":!0,"valve-6":!1,"valve-7":!1,"valve-8":!1,"valve-9":!0,"valve-10":!1,"valve-11":!1,"valve-12":!1,"valve-13":!1}),[f,h]=(0,a.useState)({"valve-1":!1}),[g,x]=(0,a.useState)({"valve-1":!1}),[p,y]=(0,a.useState)({"pump-1":!1}),{ec:m,ph:j,temp:b,setEc:w,setPh:S,setTemp:T}=_(),{subscribe:C,unsubscribe:E}=(0,r.T)();return(0,a.useEffect)(()=>{t&&e();async function e(){await (0,d.$U)({deviceId:t,keys:["INPUT_EC","INPUT_PH"]}).then(e=>{var t,l,i,n,o,a;console.log("latest data for EC and PH: ",e);let r=null==e?void 0:null===(i=e.data)||void 0===i?void 0:null===(l=i.INPUT_EC)||void 0===l?void 0:null===(t=l[0])||void 0===t?void 0:t.value,s=null==e?void 0:null===(a=e.data)||void 0===a?void 0:null===(o=a.INPUT_PH)||void 0===o?void 0:null===(n=o[0])||void 0===n?void 0:n.value;r&&w(r),s&&S(s)})}},[t]),(0,i.jsx)("div",{style:{display:"flex",flexDirection:"column",alignItems:"start"},children:(0,i.jsx)(n.Z,{style:{width:"100vw"},children:(0,i.jsx)(o.Z,{span:24,children:(0,i.jsx)("div",{style:{display:"flex",flexDirection:"column",gap:16,padding:16,marginTop:16},children:e.map(e=>{var t,l;return(null==e?void 0:null===(t=e.children)||void 0===t?void 0:t.length)===0?null:(0,i.jsx)("div",{style:{display:"flex",flexDirection:"column",gap:8,marginBottom:40},children:null==e?void 0:null===(l=e.children)||void 0===l?void 0:l.map(e=>{var t;return(null==e?void 0:null===(t=e.children)||void 0===t?void 0:t.length)===0?null:(0,i.jsxs)("div",{style:{marginBottom:40},children:[(0,i.jsx)("p",{style:{fontSize:20,fontWeight:"bold",margin:0,marginBottom:10},children:e.label}),(0,i.jsx)(n.Z,{gutter:[16,16],children:e.children.map(e=>(0,i.jsx)(o.Z,{span:12,children:"Value"===e.data_type?(0,i.jsx)(Z,{functionItem:e,lineColor:e.label.includes("hiệt độ")?"orange":e.label.includes("ec")||e.label.includes("EC")?"#438de0":e.label.includes("pH")||e.label.includes("PH")?"#a643e0":"#45c3a1"}):(0,i.jsx)(F.Z,{functionItem:e,readonly:!0})},e.label))})]},e.label)})},e.label)})})})})})}},1421:function(e,t,l){"use strict";var i=l(9341),n=l(465);t.Z=function(){let{onSuccess:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(0,n.Q)(i.A2,{onError(e,t){},onSuccess(t,l){null==e||e(t)},manual:!0})}},1584:function(e,t,l){"use strict";var i=l(9341),n=l(465);t.Z=function(){let{onSuccess:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(0,n.Q)(i.$U,{onError(e,t){},onSuccess(t,l){null==e||e(t)}})}}},function(e){e.O(0,[467,452,507,382,422,888,774,179],function(){return e(e.s=1746)}),_N_E=e.O()}]);