(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[422],{6300:function(e,t,l){"use strict";let n=(0,l(7378).createContext)({});t.Z=n},4028:function(e,t,l){"use strict";var n=l(7378),i=l(5),o=l.n(i),r=l(8539),a=l(6300),s=l(1127),u=function(e,t){var l={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(l[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,n=Object.getOwnPropertySymbols(e);i<n.length;i++)0>t.indexOf(n[i])&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(l[n[i]]=e[n[i]]);return l};function c(e){return"number"==typeof e?"".concat(e," ").concat(e," auto"):/^\d+(\.\d+)?(px|em|rem|%)$/.test(e)?"0 0 ".concat(e):e}let f=["xs","sm","md","lg","xl","xxl"],d=n.forwardRef((e,t)=>{let{getPrefixCls:l,direction:i}=n.useContext(r.E_),{gutter:d,wrap:h}=n.useContext(a.Z),{prefixCls:p,span:m,order:g,offset:b,push:v,pull:x,className:w,children:y,flex:k,style:_}=e,S=u(e,["prefixCls","span","order","offset","push","pull","className","children","flex","style"]),M=l("col",p),[E,O,P]=(0,s.cG)(M),C={},j={};f.forEach(t=>{let l={},n=e[t];"number"==typeof n?l.span=n:"object"==typeof n&&(l=n||{}),delete S[t],j=Object.assign(Object.assign({},j),{["".concat(M,"-").concat(t,"-").concat(l.span)]:void 0!==l.span,["".concat(M,"-").concat(t,"-order-").concat(l.order)]:l.order||0===l.order,["".concat(M,"-").concat(t,"-offset-").concat(l.offset)]:l.offset||0===l.offset,["".concat(M,"-").concat(t,"-push-").concat(l.push)]:l.push||0===l.push,["".concat(M,"-").concat(t,"-pull-").concat(l.pull)]:l.pull||0===l.pull,["".concat(M,"-rtl")]:"rtl"===i}),l.flex&&(j["".concat(M,"-").concat(t,"-flex")]=!0,C["--".concat(M,"-").concat(t,"-flex")]=c(l.flex))});let D=o()(M,{["".concat(M,"-").concat(m)]:void 0!==m,["".concat(M,"-order-").concat(g)]:g,["".concat(M,"-offset-").concat(b)]:b,["".concat(M,"-push-").concat(v)]:v,["".concat(M,"-pull-").concat(x)]:x},w,j,O,P),T={};if(d&&d[0]>0){let e=d[0]/2;T.paddingLeft=e,T.paddingRight=e}return k&&(T.flex=c(k),!1!==h||T.minWidth||(T.minWidth=0)),E(n.createElement("div",Object.assign({},S,{style:Object.assign(Object.assign(Object.assign({},T),_),C),className:D,ref:t}),y))});t.Z=d},923:function(e,t,l){"use strict";l.d(t,{Z:function(){return h}});var n=l(7378),i=l(5),o=l.n(i),r=l(8032),a=l(8539),s=l(4735),u=l(6300),c=l(1127),f=function(e,t){var l={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(l[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,n=Object.getOwnPropertySymbols(e);i<n.length;i++)0>t.indexOf(n[i])&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(l[n[i]]=e[n[i]]);return l};function d(e,t){let[l,i]=n.useState("string"==typeof e?e:""),o=()=>{if("string"==typeof e&&i(e),"object"==typeof e)for(let l=0;l<r.c4.length;l++){let n=r.c4[l];if(!t||!t[n])continue;let o=e[n];if(void 0!==o){i(o);return}}};return n.useEffect(()=>{o()},[JSON.stringify(e),t]),l}var h=n.forwardRef((e,t)=>{let{prefixCls:l,justify:i,align:h,className:p,style:m,children:g,gutter:b=0,wrap:v}=e,x=f(e,["prefixCls","justify","align","className","style","children","gutter","wrap"]),{getPrefixCls:w,direction:y}=n.useContext(a.E_),k=(0,s.Z)(!0,null),_=d(h,k),S=d(i,k),M=w("row",l),[E,O,P]=(0,c.VM)(M),C=function(e,t){let l=[void 0,void 0],n=Array.isArray(e)?e:[e,void 0],i=t||{xs:!0,sm:!0,md:!0,lg:!0,xl:!0,xxl:!0};return n.forEach((e,t)=>{if("object"==typeof e&&null!==e)for(let n=0;n<r.c4.length;n++){let o=r.c4[n];if(i[o]&&void 0!==e[o]){l[t]=e[o];break}}else l[t]=e}),l}(b,k),j=o()(M,{["".concat(M,"-no-wrap")]:!1===v,["".concat(M,"-").concat(S)]:S,["".concat(M,"-").concat(_)]:_,["".concat(M,"-rtl")]:"rtl"===y},p,O,P),D={},T=null!=C[0]&&C[0]>0?-(C[0]/2):void 0;T&&(D.marginLeft=T,D.marginRight=T);let[z,A]=C;D.rowGap=A;let R=n.useMemo(()=>({gutter:[z,A],wrap:v}),[z,A,v]);return E(n.createElement(u.Z.Provider,{value:R},n.createElement("div",Object.assign({},x,{className:j,style:Object.assign(Object.assign({},D),m),ref:t}),g)))})},4210:function(e,t,l){"use strict";l.d(t,{Z:function(){return H}});var n=l(7378),i=l(5),o=l.n(i),r=l(5773),a=l(8136),s=l(6535),u=l(4649),c=l(189),f=l(3940),d=l(9270),h=l(8596),p=l(7861),m=l(8070),g=l(4812),b=function(e,t){if(!e)return null;var l={left:e.offsetLeft,right:e.parentElement.clientWidth-e.clientWidth-e.offsetLeft,width:e.clientWidth,top:e.offsetTop,bottom:e.parentElement.clientHeight-e.clientHeight-e.offsetTop,height:e.clientHeight};return t?{left:0,right:0,width:0,top:l.top,bottom:l.bottom,height:l.height}:{left:l.left,right:l.right,width:l.width,top:0,bottom:0,height:0}},v=function(e){return void 0!==e?"".concat(e,"px"):void 0};function x(e){var t=e.prefixCls,l=e.containerRef,i=e.value,r=e.getValueIndex,s=e.motionName,u=e.onMotionStart,f=e.onMotionEnd,d=e.direction,h=e.vertical,x=void 0!==h&&h,w=n.useRef(null),y=n.useState(i),k=(0,a.Z)(y,2),_=k[0],S=k[1],M=function(e){var n,i=r(e),o=null===(n=l.current)||void 0===n?void 0:n.querySelectorAll(".".concat(t,"-item"))[i];return(null==o?void 0:o.offsetParent)&&o},E=n.useState(null),O=(0,a.Z)(E,2),P=O[0],C=O[1],j=n.useState(null),D=(0,a.Z)(j,2),T=D[0],z=D[1];(0,g.Z)(function(){if(_!==i){var e=M(_),t=M(i),l=b(e,x),n=b(t,x);S(i),C(l),z(n),e&&t?u():f()}},[i]);var A=n.useMemo(function(){if(x){var e;return v(null!==(e=null==P?void 0:P.top)&&void 0!==e?e:0)}return"rtl"===d?v(-(null==P?void 0:P.right)):v(null==P?void 0:P.left)},[x,d,P]),R=n.useMemo(function(){if(x){var e;return v(null!==(e=null==T?void 0:T.top)&&void 0!==e?e:0)}return"rtl"===d?v(-(null==T?void 0:T.right)):v(null==T?void 0:T.left)},[x,d,T]);return P&&T?n.createElement(m.ZP,{visible:!0,motionName:s,motionAppear:!0,onAppearStart:function(){return x?{transform:"translateY(var(--thumb-start-top))",height:"var(--thumb-start-height)"}:{transform:"translateX(var(--thumb-start-left))",width:"var(--thumb-start-width)"}},onAppearActive:function(){return x?{transform:"translateY(var(--thumb-active-top))",height:"var(--thumb-active-height)"}:{transform:"translateX(var(--thumb-active-left))",width:"var(--thumb-active-width)"}},onVisibleChanged:function(){C(null),z(null),f()}},function(e,l){var i=e.className,r=e.style,a=(0,c.Z)((0,c.Z)({},r),{},{"--thumb-start-left":A,"--thumb-start-width":v(null==P?void 0:P.width),"--thumb-active-left":R,"--thumb-active-width":v(null==T?void 0:T.width),"--thumb-start-top":A,"--thumb-start-height":v(null==P?void 0:P.height),"--thumb-active-top":R,"--thumb-active-height":v(null==T?void 0:T.height)}),s={ref:(0,p.sQ)(w,l),style:a,className:o()("".concat(t,"-thumb"),i)};return n.createElement("div",s)}):null}var w=["prefixCls","direction","vertical","options","disabled","defaultValue","value","name","onChange","className","motionName"],y=function(e){var t=e.prefixCls,l=e.className,i=e.disabled,r=e.checked,a=e.label,s=e.title,c=e.value,f=e.name,d=e.onChange,h=e.onFocus,p=e.onBlur,m=e.onKeyDown,g=e.onKeyUp,b=e.onMouseDown;return n.createElement("label",{className:o()(l,(0,u.Z)({},"".concat(t,"-item-disabled"),i)),onMouseDown:b},n.createElement("input",{name:f,className:"".concat(t,"-item-input"),type:"radio",disabled:i,checked:r,onChange:function(e){i||d(e,c)},onFocus:h,onBlur:p,onKeyDown:m,onKeyUp:g}),n.createElement("div",{className:"".concat(t,"-item-label"),title:s,"aria-selected":r},a))},k=n.forwardRef(function(e,t){var l,i,m=e.prefixCls,g=void 0===m?"rc-segmented":m,b=e.direction,v=e.vertical,k=e.options,_=void 0===k?[]:k,S=e.disabled,M=e.defaultValue,E=e.value,O=e.name,P=e.onChange,C=e.className,j=e.motionName,D=(0,s.Z)(e,w),T=n.useRef(null),z=n.useMemo(function(){return(0,p.sQ)(T,t)},[T,t]),A=n.useMemo(function(){return _.map(function(e){if("object"===(0,f.Z)(e)&&null!==e){var t=function(e){if(void 0!==e.title)return e.title;if("object"!==(0,f.Z)(e.label)){var t;return null===(t=e.label)||void 0===t?void 0:t.toString()}}(e);return(0,c.Z)((0,c.Z)({},e),{},{title:t})}return{label:null==e?void 0:e.toString(),title:null==e?void 0:e.toString(),value:e}})},[_]),R=(0,d.Z)(null===(l=A[0])||void 0===l?void 0:l.value,{value:E,defaultValue:M}),H=(0,a.Z)(R,2),N=H[0],Z=H[1],I=n.useState(!1),W=(0,a.Z)(I,2),Y=W[0],L=W[1],B=function(e,t){Z(t),null==P||P(t)},G=(0,h.Z)(D,["children"]),F=n.useState(!1),V=(0,a.Z)(F,2),U=V[0],q=V[1],X=n.useState(!1),$=(0,a.Z)(X,2),K=$[0],J=$[1],Q=function(){J(!0)},ee=function(){J(!1)},et=function(){q(!1)},el=function(e){"Tab"===e.key&&q(!0)},en=function(e){var t=A.findIndex(function(e){return e.value===N}),l=A.length,n=A[(t+e+l)%l];n&&(Z(n.value),null==P||P(n.value))},ei=function(e){switch(e.key){case"ArrowLeft":case"ArrowUp":en(-1);break;case"ArrowRight":case"ArrowDown":en(1)}};return n.createElement("div",(0,r.Z)({role:"radiogroup","aria-label":"segmented control",tabIndex:S?void 0:0},G,{className:o()(g,(i={},(0,u.Z)(i,"".concat(g,"-rtl"),"rtl"===b),(0,u.Z)(i,"".concat(g,"-disabled"),S),(0,u.Z)(i,"".concat(g,"-vertical"),v),i),void 0===C?"":C),ref:z}),n.createElement("div",{className:"".concat(g,"-group")},n.createElement(x,{vertical:v,prefixCls:g,value:N,containerRef:T,motionName:"".concat(g,"-").concat(void 0===j?"thumb-motion":j),direction:b,getValueIndex:function(e){return A.findIndex(function(t){return t.value===e})},onMotionStart:function(){L(!0)},onMotionEnd:function(){L(!1)}}),A.map(function(e){var t;return n.createElement(y,(0,r.Z)({},e,{name:O,key:e.value,prefixCls:g,className:o()(e.className,"".concat(g,"-item"),(t={},(0,u.Z)(t,"".concat(g,"-item-selected"),e.value===N&&!Y),(0,u.Z)(t,"".concat(g,"-item-focused"),K&&U&&e.value===N),t)),checked:e.value===N,onChange:B,onFocus:Q,onBlur:ee,onKeyDown:ei,onKeyUp:el,onMouseDown:et,disabled:!!S||!!e.disabled}))})))}),_=l(526),S=l(8539),M=l(9801),E=l(7349),O=l(5334),P=l(4547),C=l(4645);function j(e,t){return{["".concat(e,", ").concat(e,":hover, ").concat(e,":focus")]:{color:t.colorTextDisabled,cursor:"not-allowed"}}}function D(e){return{backgroundColor:e.itemSelectedBg,boxShadow:e.boxShadowTertiary}}let T=Object.assign({overflow:"hidden"},O.vS),z=e=>{let{componentCls:t}=e,l=e.calc(e.controlHeight).sub(e.calc(e.trackPadding).mul(2)).equal(),n=e.calc(e.controlHeightLG).sub(e.calc(e.trackPadding).mul(2)).equal(),i=e.calc(e.controlHeightSM).sub(e.calc(e.trackPadding).mul(2)).equal();return{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},(0,O.Wf)(e)),{display:"inline-block",padding:e.trackPadding,color:e.itemColor,background:e.trackBg,borderRadius:e.borderRadius,transition:"all ".concat(e.motionDurationMid," ").concat(e.motionEaseInOut)}),(0,O.Qy)(e)),{["".concat(t,"-group")]:{position:"relative",display:"flex",alignItems:"stretch",justifyItems:"flex-start",flexDirection:"row",width:"100%"},["&".concat(t,"-rtl")]:{direction:"rtl"},["&".concat(t,"-vertical")]:{["".concat(t,"-group")]:{flexDirection:"column"},["".concat(t,"-thumb")]:{width:"100%",height:0,padding:"0 ".concat((0,E.bf)(e.paddingXXS))}},["&".concat(t,"-block")]:{display:"flex"},["&".concat(t,"-block ").concat(t,"-item")]:{flex:1,minWidth:0},["".concat(t,"-item")]:{position:"relative",textAlign:"center",cursor:"pointer",transition:"color ".concat(e.motionDurationMid," ").concat(e.motionEaseInOut),borderRadius:e.borderRadiusSM,transform:"translateZ(0)","&-selected":Object.assign(Object.assign({},D(e)),{color:e.itemSelectedColor}),"&-focused":Object.assign({},(0,O.oN)(e)),"&::after":{content:'""',position:"absolute",zIndex:-1,width:"100%",height:"100%",top:0,insetInlineStart:0,borderRadius:"inherit",opacity:0,transition:"opacity ".concat(e.motionDurationMid),pointerEvents:"none"},["&:hover:not(".concat(t,"-item-selected):not(").concat(t,"-item-disabled)")]:{color:e.itemHoverColor,"&::after":{opacity:1,backgroundColor:e.itemHoverBg}},["&:active:not(".concat(t,"-item-selected):not(").concat(t,"-item-disabled)")]:{color:e.itemHoverColor,"&::after":{opacity:1,backgroundColor:e.itemActiveBg}},"&-label":Object.assign({minHeight:l,lineHeight:(0,E.bf)(l),padding:"0 ".concat((0,E.bf)(e.segmentedPaddingHorizontal))},T),"&-icon + *":{marginInlineStart:e.calc(e.marginSM).div(2).equal()},"&-input":{position:"absolute",insetBlockStart:0,insetInlineStart:0,width:0,height:0,opacity:0,pointerEvents:"none"}},["".concat(t,"-thumb")]:Object.assign(Object.assign({},D(e)),{position:"absolute",insetBlockStart:0,insetInlineStart:0,width:0,height:"100%",padding:"".concat((0,E.bf)(e.paddingXXS)," 0"),borderRadius:e.borderRadiusSM,transition:"transform ".concat(e.motionDurationSlow," ").concat(e.motionEaseInOut,", height ").concat(e.motionDurationSlow," ").concat(e.motionEaseInOut),["& ~ ".concat(t,"-item:not(").concat(t,"-item-selected):not(").concat(t,"-item-disabled)::after")]:{backgroundColor:"transparent"}}),["&".concat(t,"-lg")]:{borderRadius:e.borderRadiusLG,["".concat(t,"-item-label")]:{minHeight:n,lineHeight:(0,E.bf)(n),padding:"0 ".concat((0,E.bf)(e.segmentedPaddingHorizontal)),fontSize:e.fontSizeLG},["".concat(t,"-item, ").concat(t,"-thumb")]:{borderRadius:e.borderRadius}},["&".concat(t,"-sm")]:{borderRadius:e.borderRadiusSM,["".concat(t,"-item-label")]:{minHeight:i,lineHeight:(0,E.bf)(i),padding:"0 ".concat((0,E.bf)(e.segmentedPaddingHorizontalSM))},["".concat(t,"-item, ").concat(t,"-thumb")]:{borderRadius:e.borderRadiusXS}}}),j("&-disabled ".concat(t,"-item"),e)),j("".concat(t,"-item-disabled"),e)),{["".concat(t,"-thumb-motion-appear-active")]:{transition:"transform ".concat(e.motionDurationSlow," ").concat(e.motionEaseInOut,", width ").concat(e.motionDurationSlow," ").concat(e.motionEaseInOut),willChange:"transform, width"},["&".concat(t,"-shape-round")]:{borderRadius:9999,["".concat(t,"-item, ").concat(t,"-thumb")]:{borderRadius:9999}}})}};var A=(0,P.I$)("Segmented",e=>{let{lineWidth:t,calc:l}=e;return[z((0,C.IX)(e,{segmentedPaddingHorizontal:l(e.controlPaddingHorizontal).sub(t).equal(),segmentedPaddingHorizontalSM:l(e.controlPaddingHorizontalSM).sub(t).equal()}))]},e=>{let{colorTextLabel:t,colorText:l,colorFillSecondary:n,colorBgElevated:i,colorFill:o,lineWidthBold:r,colorBgLayout:a}=e;return{trackPadding:r,trackBg:a,itemColor:t,itemHoverColor:l,itemHoverBg:n,itemSelectedBg:i,itemActiveBg:o,itemSelectedColor:l}}),R=function(e,t){var l={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(l[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,n=Object.getOwnPropertySymbols(e);i<n.length;i++)0>t.indexOf(n[i])&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(l[n[i]]=e[n[i]]);return l},H=n.forwardRef((e,t)=>{let l=(0,_.Z)(),{prefixCls:i,className:r,rootClassName:a,block:s,options:u=[],size:c="middle",style:f,vertical:d,shape:h="default",name:p=l}=e,m=R(e,["prefixCls","className","rootClassName","block","options","size","style","vertical","shape","name"]),{getPrefixCls:g,direction:b,className:v,style:x}=(0,S.dj)("segmented"),w=g("segmented",i),[y,E,O]=A(w),P=(0,M.Z)(c),C=n.useMemo(()=>u.map(e=>{if("object"==typeof e&&(null==e?void 0:e.icon)){let{icon:t,label:l}=e;return Object.assign(Object.assign({},R(e,["icon","label"])),{label:n.createElement(n.Fragment,null,n.createElement("span",{className:"".concat(w,"-item-icon")},t),l&&n.createElement("span",null,l))})}return e}),[u,w]),j=o()(r,a,v,{["".concat(w,"-block")]:s,["".concat(w,"-sm")]:"small"===P,["".concat(w,"-lg")]:"large"===P,["".concat(w,"-vertical")]:d,["".concat(w,"-shape-").concat(h)]:"round"===h},E,O),D=Object.assign(Object.assign({},x),f);return y(n.createElement(k,Object.assign({},m,{name:p,className:j,style:D,options:C,ref:t,prefixCls:w,direction:b,vertical:d})))})},489:function(){},4836:function(e,t,l){"use strict";let n,i;l.d(t,{Z:function(){return lj}});let o="u-off",r="u-label",a="width",s="height",u="bottom",c="left",f="right",d="#000",h=d+"0",p="mousemove",m="mousedown",g="mouseup",b="mouseenter",v="mouseleave",x="dblclick",w="change",y="dppxchange",k="undefined"!=typeof window,_=k?document:null,S=k?window:null,M=k?navigator:null;function E(e,t){if(null!=t){let l=e.classList;l.contains(t)||l.add(t)}}function O(e,t){let l=e.classList;l.contains(t)&&l.remove(t)}function P(e,t,l){e.style[t]=l+"px"}function C(e,t,l,n){let i=_.createElement(e);return null!=t&&E(i,t),null!=l&&l.insertBefore(i,n),i}function j(e,t){return C("div",e,t)}let D=new WeakMap;function T(e,t,l,n,i){let r="translate("+t+"px,"+l+"px)";r!=D.get(e)&&(e.style.transform=r,D.set(e,r),t<0||l<0||t>n||l>i?E(e,o):O(e,o))}let z=new WeakMap;function A(e,t,l){let n=t+l;n!=z.get(e)&&(z.set(e,n),e.style.background=t,e.style.borderColor=l)}let R=new WeakMap;function H(e,t,l,n){let i=t+""+l;i!=R.get(e)&&(R.set(e,i),e.style.height=l+"px",e.style.width=t+"px",e.style.marginLeft=n?-t/2+"px":0,e.style.marginTop=n?-l/2+"px":0)}let N={passive:!0},Z={...N,capture:!0};function I(e,t,l,n){t.addEventListener(e,l,n?Z:N)}function W(e,t,l,n){t.removeEventListener(e,l,N)}function Y(e,t,l,n){let i;l=l||0;let o=(n=n||t.length-1)<=2147483647;for(;n-l>1;)t[i=o?l+n>>1:er((l+n)/2)]<e?l=i:n=i;return e-t[l]<=t[n]-e?l:n}function L(e){return(t,l,n)=>{let i=-1,o=-1;for(let o=l;o<=n;o++)if(e(t[o])){i=o;break}for(let i=n;i>=l;i--)if(e(t[i])){o=i;break}return[i,o]}}k&&function e(){let t=devicePixelRatio;n!=t&&(n=t,i&&W(w,i,e),I(w,i=matchMedia(`(min-resolution: ${n-.001}dppx) and (max-resolution: ${n+.001}dppx)`),e),S.dispatchEvent(new CustomEvent(y)))}();let B=e=>null!=e,G=e=>null!=e&&e>0,F=L(B),V=L(G);function U(e,t,l,n){let i=ed(e),o=ed(t);e==t&&(-1==i?(e*=l,t/=l):(e/=l,t*=l));let r=10==l?eh:ep,a=(1==i?er:es)(r(eo(e))),s=(1==o?es:er)(r(eo(t))),u=ef(l,a),c=ef(l,s);return 10==l&&(a<0&&(u=eT(u,-a)),s<0&&(c=eT(c,-s))),n||2==l?(e=u*i,t=c*o):(e=eD(e,u),t=ej(t,c)),[e,t]}function q(e,t,l,n){let i=U(e,t,l,n);return 0==e&&(i[0]=0),0==t&&(i[1]=0),i}let X={mode:3,pad:.1},$={pad:0,soft:null,mode:0},K={min:$,max:$};function J(e,t,l,n){return eB(l)?ee(e,t,l):($.pad=l,$.soft=n?0:null,$.mode=n?3:0,ee(e,t,K))}function Q(e,t){return null==e?t:e}function ee(e,t,l){let n=l.min,i=l.max,o=Q(n.pad,0),r=Q(i.pad,0),a=Q(n.hard,-eb),s=Q(i.hard,eb),u=Q(n.soft,eb),c=Q(i.soft,-eb),f=Q(n.mode,0),d=Q(i.mode,0),h=t-e,p=eh(h),m=ec(eo(e),eo(t)),g=eo(eh(m)-p);(h<1e-24||g>10)&&(h=0,(0==e||0==t)&&(h=1e-24,2==f&&u!=eb&&(o=0),2==d&&c!=-eb&&(r=0)));let b=h||m||1e3,v=ef(10,er(eh(b))),x=b*(0==h?0==e?.1:1:o),w=eT(eD(e-x,v/10),24),y=e>=u&&(1==f||3==f&&w<=u||2==f&&w>=u)?u:eb,k=ec(a,w<y&&e>=y?y:eu(y,w)),_=b*(0==h?0==t?.1:1:r),S=eT(ej(t+_,v/10),24),M=t<=c&&(1==d||3==d&&S>=c||2==d&&S<=c)?c:-eb,E=eu(s,S>M&&t<=M?M:ec(M,S));return k==E&&0==k&&(E=100),[k,E]}let et=new Intl.NumberFormat(k?M.language:"en-US"),el=e=>et.format(e),en=Math,ei=en.PI,eo=en.abs,er=en.floor,ea=en.round,es=en.ceil,eu=en.min,ec=en.max,ef=en.pow,ed=en.sign,eh=en.log10,ep=en.log2,em=(e,t=1)=>en.sinh(e)*t,eg=(e,t=1)=>en.asinh(e/t),eb=1/0;function ev(e){return(0|eh((e^e>>31)-(e>>31)))+1}function ex(e,t,l){return eu(ec(e,t),l)}function ew(e){return"function"==typeof e}function ey(e){return ew(e)?e:()=>e}let ek=e=>e,e_=(e,t)=>t,eS=e=>null,eM=e=>!0,eE=(e,t)=>e==t,eO=/\.\d*?(?=9{6,}|0{6,})/gm,eP=e=>{if(eW(e)||ez.has(e))return e;let t=`${e}`,l=t.match(eO);if(null==l)return e;let n=l[0].length-1;if(-1!=t.indexOf("e-")){let[e,l]=t.split("e");return+`${eP(e)}e${l}`}return eT(e,n)};function eC(e,t){return eP(eT(eP(e/t))*t)}function ej(e,t){return eP(es(eP(e/t))*t)}function eD(e,t){return eP(er(eP(e/t))*t)}function eT(e,t=0){if(eW(e))return e;let l=10**t;return ea(e*l*(1+Number.EPSILON))/l}let ez=new Map;function eA(e){return((""+e).split(".")[1]||"").length}function eR(e,t,l,n){let i=[],o=n.map(eA);for(let r=t;r<l;r++){let t=eo(r),l=eT(ef(e,r),t);for(let a=0;a<n.length;a++){let s=10==e?+`${n[a]}e${r}`:n[a]*l,u=(r>=0?0:t)+(r>=o[a]?0:o[a]),c=10==e?s:eT(s,u);i.push(c),ez.set(c,u)}}return i}let eH={},eN=[],eZ=[null,null],eI=Array.isArray,eW=Number.isInteger,eY=e=>void 0===e;function eL(e){return"string"==typeof e}function eB(e){let t=!1;if(null!=e){let l=e.constructor;t=null==l||l==Object}return t}function eG(e){return null!=e&&"object"==typeof e}let eF=Object.getPrototypeOf(Uint8Array),eV="__proto__";function eU(e,t=eB){let l;if(eI(e)){let n=e.find(e=>null!=e);if(eI(n)||t(n)){l=Array(e.length);for(let n=0;n<e.length;n++)l[n]=eU(e[n],t)}else l=e.slice()}else if(e instanceof eF)l=e.slice();else if(t(e))for(let n in l={},e)n!=eV&&(l[n]=eU(e[n],t));else l=e;return l}function eq(e){let t=arguments;for(let l=1;l<t.length;l++){let n=t[l];for(let t in n)t!=eV&&(eB(e[t])?eq(e[t],eU(n[t])):e[t]=eU(n[t]))}return e}let eX="undefined"==typeof queueMicrotask?e=>Promise.resolve().then(e):queueMicrotask,e$=["January","February","March","April","May","June","July","August","September","October","November","December"],eK=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"];function eJ(e){return e.slice(0,3)}let eQ=eK.map(eJ),e0=e$.map(eJ),e1={MMMM:e$,MMM:e0,WWWW:eK,WWW:eQ};function e2(e){return(e<10?"0":"")+e}let e3={YYYY:e=>e.getFullYear(),YY:e=>(e.getFullYear()+"").slice(2),MMMM:(e,t)=>t.MMMM[e.getMonth()],MMM:(e,t)=>t.MMM[e.getMonth()],MM:e=>e2(e.getMonth()+1),M:e=>e.getMonth()+1,DD:e=>e2(e.getDate()),D:e=>e.getDate(),WWWW:(e,t)=>t.WWWW[e.getDay()],WWW:(e,t)=>t.WWW[e.getDay()],HH:e=>e2(e.getHours()),H:e=>e.getHours(),h:e=>{let t=e.getHours();return 0==t?12:t>12?t-12:t},AA:e=>e.getHours()>=12?"PM":"AM",aa:e=>e.getHours()>=12?"pm":"am",a:e=>e.getHours()>=12?"p":"a",mm:e=>e2(e.getMinutes()),m:e=>e.getMinutes(),ss:e=>e2(e.getSeconds()),s:e=>e.getSeconds(),fff:e=>{var t;return((t=e.getMilliseconds())<10?"00":t<100?"0":"")+t}};function e5(e,t){t=t||e1;let l=[],n=/\{([a-z]+)\}|[^{]+/gi,i;for(;i=n.exec(e);)l.push("{"==i[0][0]?e3[i[1]]:i[0]);return e=>{let n="";for(let i=0;i<l.length;i++)n+="string"==typeof l[i]?l[i]:l[i](e,t);return n}}let e4=new Intl.DateTimeFormat().resolvedOptions().timeZone,e7=e=>e%1==0,e8=[1,2,2.5,5],e6=eR(10,-32,0,e8),e9=eR(10,0,32,e8),te=e9.filter(e7),tt=e6.concat(e9),tl="{YYYY}",tn="\n"+tl,ti="{M}/{D}",to="\n"+ti,tr=to+"/{YY}",ta="{aa}",ts="{h}:{mm}"+ta,tu="\n"+ts,tc=":{ss}";function tf(e){let t=1e3*e,l=60*t,n=60*l,i=24*n,o=30*i,r=365*i;return[(1==e?eR(10,0,3,e8).filter(e7):eR(10,-3,0,e8)).concat([t,5*t,10*t,15*t,30*t,l,5*l,10*l,15*l,30*l,n,2*n,3*n,4*n,6*n,8*n,12*n,i,2*i,3*i,4*i,5*i,6*i,7*i,8*i,9*i,10*i,15*i,o,2*o,3*o,4*o,6*o,r,2*r,5*r,10*r,25*r,50*r,100*r]),[[r,tl,null,null,null,null,null,null,1],[28*i,"{MMM}",tn,null,null,null,null,null,1],[i,ti,tn,null,null,null,null,null,1],[n,"{h}"+ta,tr,null,to,null,null,null,1],[l,ts,tr,null,to,null,null,null,1],[t,tc,tr+" "+ts,null,to+" "+ts,null,tu,null,1],[e,tc+".{fff}",tr+" "+ts,null,to+" "+ts,null,tu,null,1]],function(t){return(a,s,u,c,f,d)=>{let h=[],p=f>=r,m=f>=o&&f<r,g=t(u),b=eT(g*e,3),v=tw(g.getFullYear(),p?0:g.getMonth(),m||p?1:g.getDate()),x=eT(v*e,3);if(m||p){let l=m?f/o:0,n=p?f/r:0,i=b==x?b:eT(tw(v.getFullYear()+n,v.getMonth()+l,1)*e,3),a=new Date(ea(i/e)),s=a.getFullYear(),u=a.getMonth();for(let o=0;i<=c;o++){let r=tw(s+n*o,u+l*o,1),a=r-t(eT(r*e,3));(i=eT((+r+a)*e,3))<=c&&h.push(i)}}else{let o=x+(er(u)-er(b))+ej(b-x,f>=i?i:f);h.push(o);let r=t(o),p=r.getHours()+r.getMinutes()/l+r.getSeconds()/n,m=f/n,g=d/a.axes[s]._space;for(;!((o=eT(o+f,1==e?0:3))>c);)if(m>1){let e=er(eT(p+m,6))%24,l=t(o).getHours()-e;l>1&&(l=-1),o-=l*n,p=(p+m)%24,eT((o-h[h.length-1])/f,3)*g>=.7&&h.push(o)}else h.push(o)}return h}}]}let[td,th,tp]=tf(1),[tm,tg,tb]=tf(.001);function tv(e,t){return e.map(e=>e.map((l,n)=>0==n||8==n||null==l?l:t(1==n||0==e[8]?l:e[1]+l)))}function tx(e,t){return(l,n,i,o,r)=>{let a,s,u,c,f,d,h=t.find(e=>r>=e[0])||t[t.length-1];return n.map(t=>{let l=e(t),n=l.getFullYear(),i=l.getMonth(),o=l.getDate(),r=l.getHours(),p=l.getMinutes(),m=l.getSeconds(),g=n!=a&&h[2]||i!=s&&h[3]||o!=u&&h[4]||r!=c&&h[5]||p!=f&&h[6]||m!=d&&h[7]||h[1];return a=n,s=i,u=o,c=r,f=p,d=m,g(l)})}}function tw(e,t,l){return new Date(e,t,l)}function ty(e,t){return(l,n,i,o)=>null==o?"--":t(e(n))}eR(2,-53,53,[1]);let tk={show:!0,live:!0,isolate:!1,mount:()=>{},markers:{show:!0,width:2,stroke:function(e,t){let l=e.series[t];return l.width?l.stroke(e,t):l.points.width?l.points.stroke(e,t):null},fill:function(e,t){return e.series[t].fill(e,t)},dash:"solid"},idx:null,idxs:null,values:[]},t_=[0,0];function tS(e,t,l,n=!0){return e=>{0!=e.button||n&&e.target!=t||l(e)}}function tM(e,t,l,n=!0){return e=>{n&&e.target!=t||l(e)}}let tE={show:!0,x:!0,y:!0,lock:!1,move:function(e,t,l){return t_[0]=t,t_[1]=l,t_},points:{one:!1,show:function(e,t){let l=e.cursor.points,n=j(),i=l.size(e,t);P(n,a,i),P(n,s,i);let o=-(i/2);P(n,"marginLeft",o),P(n,"marginTop",o);let r=l.width(e,t,i);return r&&P(n,"borderWidth",r),n},size:function(e,t){return e.series[t].points.size},width:0,stroke:function(e,t){let l=e.series[t].points;return l._stroke||l._fill},fill:function(e,t){let l=e.series[t].points;return l._fill||l._stroke}},bind:{mousedown:tS,mouseup:tS,click:tS,dblclick:tS,mousemove:tM,mouseleave:tM,mouseenter:tM},drag:{setScale:!0,x:!0,y:!1,dist:0,uni:null,click:(e,t)=>{t.stopPropagation(),t.stopImmediatePropagation()},_x:!1,_y:!1},focus:{dist:(e,t,l,n,i)=>n-i,prox:-1,bias:0},hover:{skip:[void 0],prox:null,bias:0},left:-10,top:-10,idx:null,dataIdx:null,idxs:null,event:null},tO={show:!0,stroke:"rgba(0,0,0,0.07)",width:2},tP=eq({},tO,{filter:e_}),tC=eq({},tP,{size:10}),tj=eq({},tO,{show:!1}),tD='12px system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',tT="bold "+tD,tz={show:!0,scale:"x",stroke:d,space:50,gap:5,alignTo:1,size:50,labelGap:0,labelSize:30,labelFont:tT,side:2,grid:tP,ticks:tC,border:tj,font:tD,lineGap:1.5,rotate:0},tA={show:!0,scale:"x",auto:!1,sorted:1,min:eb,max:-eb,idxs:[]};function tR(e,t,l,n,i){return t.map(e=>null==e?"":el(e))}function tH(e,t,l,n,i,o,r){let a=[],s=ez.get(i)||0;l=r?l:eT(ej(l,i),s);for(let e=l;e<=n;e=eT(e+i,s))a.push(Object.is(e,-0)?0:e);return a}function tN(e,t,l,n,i,o,r){let a=[],s=e.scales[e.axes[t].scale].log,u=er((10==s?eh:ep)(l));i=ef(s,u),10==s&&(i=tt[Y(i,tt)]);let c=l,f=i*s;10==s&&(f=tt[Y(f,tt)]);do a.push(c),c+=i,10!=s||ez.has(c)||(c=eT(c,ez.get(i))),c>=f&&(f=(i=c)*s,10==s&&(f=tt[Y(f,tt)]));while(c<=n);return a}function tZ(e,t,l,n,i,o,r){let a=e.scales[e.axes[t].scale].asinh,s=n>a?tN(e,t,ec(a,l),n,i):[a],u=n>=0&&l<=0?[0]:[];return(l<-a?tN(e,t,ec(a,-n),-l,i):[a]).reverse().map(e=>-e).concat(u,s)}let tI=/./,tW=/[12357]/,tY=/[125]/,tL=/1/,tB=(e,t,l,n)=>e.map((e,i)=>4==t&&0==e||i%n==0&&l.test(e.toExponential()[e<0?1:0])?e:null);function tG(e,t,l,n,i){let o=e.axes[l],r=o.scale,a=e.scales[r],s=e.valToPos,u=o._space,c=s(10,r),f=s(9,r)-c>=u?tI:s(7,r)-c>=u?tW:s(5,r)-c>=u?tY:tL;if(f==tL){let e=eo(s(1,r)-c);if(e<u)return tB(t.slice().reverse(),a.distr,f,es(u/e)).reverse()}return tB(t,a.distr,f,1)}function tF(e,t,l,n,i){let o=e.axes[l],r=o.scale,a=o._space,s=e.valToPos,u=eo(s(1,r)-s(2,r));return u<a?tB(t.slice().reverse(),3,tI,es(a/u)).reverse():t}function tV(e,t,l,n){return null==n?"--":null==t?"":el(t)}let tU={show:!0,scale:"y",stroke:d,space:30,gap:5,alignTo:1,size:50,labelGap:0,labelSize:30,labelFont:tT,side:3,grid:tP,ticks:tC,border:tj,font:tD,lineGap:1.5,rotate:0},tq={scale:null,auto:!0,sorted:0,min:eb,max:-eb},tX=(e,t,l,n,i)=>i,t$={show:!0,auto:!0,sorted:0,gaps:tX,alpha:1,facets:[eq({},tq,{scale:"x"}),eq({},tq,{scale:"y"})]},tK={scale:"y",auto:!0,sorted:0,show:!0,spanGaps:!1,gaps:tX,alpha:1,points:{show:function(e,t){let{scale:l,idxs:i}=e.series[0],o=e._data[0],r=e.valToPos(o[i[0]],l,!0),a=eo(e.valToPos(o[i[1]],l,!0)-r)/(e.series[t].points.space*n);return i[1]-i[0]<=a},filter:null},values:null,min:eb,max:-eb,idxs:[],path:null,clip:null};function tJ(e,t,l,n,i){return l/10}let tQ={time:!0,auto:!0,distr:1,log:10,asinh:1,min:null,max:null,dir:1,ori:0},t0=eq({},tQ,{time:!1,ori:1}),t1={};function t2(e,t){let l=t1[e];return l||(l={key:e,plots:[],sub(e){l.plots.push(e)},unsub(e){l.plots=l.plots.filter(t=>t!=e)},pub(e,t,n,i,o,r,a){for(let s=0;s<l.plots.length;s++)l.plots[s]!=t&&l.plots[s].pub(e,t,n,i,o,r,a)}},null==e||(t1[e]=l)),l}function t3(e,t,l){let n=e.mode,i=e.series[t],o=2==n?e._data[t]:e._data,r=e.scales,a=e.bbox,s=o[0],u=2==n?o[1]:o[t],c=2==n?r[i.facets[0].scale]:r[e.series[0].scale],f=2==n?r[i.facets[1].scale]:r[i.scale],d=a.left,h=a.top,p=a.width,m=a.height,g=e.valToPosH,b=e.valToPosV;return 0==c.ori?l(i,s,u,c,f,g,b,d,h,p,m,lt,ln,lo,la,lu):l(i,s,u,c,f,b,g,h,d,m,p,ll,li,lr,ls,lc)}function t5(e,t){let l=0,n=0,i=Q(e.bands,eN);for(let e=0;e<i.length;e++){let o=i[e];o.series[0]==t?l=o.dir:o.series[1]==t&&(1==o.dir?n|=1:n|=2)}return[l,1==n?-1:2==n?1:3==n?2:0]}function t4(e,t,l,n,i){let o=e.mode,r=e.series[t],a=2==o?r.facets[1].scale:r.scale,s=e.scales[a];return -1==i?s.min:1==i?s.max:3==s.distr?1==s.dir?s.min:s.max:0}function t7(e,t,l,n,i,o){return t3(e,t,(e,t,r,a,s,u,c,f,d,h,p)=>{let m,g,b=e.pxRound,v=a.dir*(0==a.ori?1:-1),x=0==a.ori?ln:li;1==v?(m=l,g=n):(m=n,g=l);let w=b(u(t[m],a,h,f)),y=b(c(r[m],s,p,d)),k=b(u(t[g],a,h,f)),_=b(c(1==o?s.max:s.min,s,p,d)),S=new Path2D(i);return x(S,k,_),x(S,w,_),x(S,w,y),S})}function t8(e,t,l,n,i,o){let r=null;if(e.length>0){r=new Path2D;let a=0==t?lo:lr,s=l;for(let t=0;t<e.length;t++){let l=e[t];if(l[1]>l[0]){let e=l[0]-s;e>0&&a(r,s,n,e,n+o),s=l[1]}}let u=l+i-s;u>0&&a(r,s,n-5,u,n+o+10)}return r}function t6(e,t,l,n,i,o,r){let a=[],s=e.length;for(let u=1==i?l:n;u>=l&&u<=n;u+=i)if(null===t[u]){let c=u,f=u;if(1==i)for(;++u<=n&&null===t[u];)f=u;else for(;--u>=l&&null===t[u];)f=u;let d=o(e[c]),h=f==c?d:o(e[f]),p=c-i;d=r<=0&&p>=0&&p<s?o(e[p]):d;let m=f+i;(h=r>=0&&m>=0&&m<s?o(e[m]):h)>=d&&a.push([d,h])}return a}function t9(e){return 0==e?ek:1==e?ea:t=>eC(t,e)}function le(e){let t=0==e?lt:ll,l=0==e?(e,t,l,n,i,o)=>{e.arcTo(t,l,n,i,o)}:(e,t,l,n,i,o)=>{e.arcTo(l,t,i,n,o)},n=0==e?(e,t,l,n,i)=>{e.rect(t,l,n,i)}:(e,t,l,n,i)=>{e.rect(l,t,i,n)};return(e,i,o,r,a,s=0,u=0)=>{0==s&&0==u?n(e,i,o,r,a):(s=eu(s,r/2,a/2),u=eu(u,r/2,a/2),t(e,i+s,o),l(e,i+r,o,i+r,o+a,s),l(e,i+r,o+a,i,o+a,u),l(e,i,o+a,i,o,u),l(e,i,o,i+r,o,s),e.closePath())}}let lt=(e,t,l)=>{e.moveTo(t,l)},ll=(e,t,l)=>{e.moveTo(l,t)},ln=(e,t,l)=>{e.lineTo(t,l)},li=(e,t,l)=>{e.lineTo(l,t)},lo=le(0),lr=le(1),la=(e,t,l,n,i,o)=>{e.arc(t,l,n,i,o)},ls=(e,t,l,n,i,o)=>{e.arc(l,t,n,i,o)},lu=(e,t,l,n,i,o,r)=>{e.bezierCurveTo(t,l,n,i,o,r)},lc=(e,t,l,n,i,o,r)=>{e.bezierCurveTo(l,t,i,n,r,o)};function lf(e){return(e,t,l,i,o)=>t3(e,t,(t,r,a,s,u,c,f,d,h,p,m)=>{let g,b,{pxRound:v,points:x}=t;0==s.ori?(g=lt,b=la):(g=ll,b=ls);let w=eT(x.width*n,3),y=(x.size-x.width)/2*n,k=eT(2*y,3),_=new Path2D,S=new Path2D,{left:M,top:E,width:O,height:P}=e.bbox;lo(S,M-k,E-k,O+2*k,P+2*k);let C=e=>{if(null!=a[e]){let t=v(c(r[e],s,p,d)),l=v(f(a[e],u,m,h));g(_,t+y,l),b(_,t,l,y,0,2*ei)}};if(o)o.forEach(C);else for(let e=l;e<=i;e++)C(e);return{stroke:w>0?_:null,fill:_,clip:S,flags:3}})}function ld(e){return(t,l,n,i,o,r)=>{n!=i&&(o!=n&&r!=n&&e(t,l,n),o!=i&&r!=i&&e(t,l,i),e(t,l,r))}}let lh=ld(ln),lp=ld(li);function lm(e){let t=Q(e?.alignGaps,0);return(e,l,n,i)=>t3(e,l,(o,r,a,s,u,c,f,d,h,p,m)=>{let g,b;[n,i]=F(a,n,i);let v=o.pxRound,x=e=>v(c(e,s,p,d)),w=e=>v(f(e,u,m,h));0==s.ori?(g=ln,b=lh):(g=li,b=lp);let y=s.dir*(0==s.ori?1:-1),k={stroke:new Path2D,fill:null,clip:null,band:null,gaps:null,flags:1},_=k.stroke,S=!1;if(i-n>=4*p){let t=t=>e.posToVal(t,s.key,!0),l=null,o=null,u,c,f,d=x(r[1==y?n:i]),h=x(r[n]),p=x(r[i]),m=t(1==y?h+1:p-1);for(let e=1==y?n:i;e>=n&&e<=i;e+=y){let n=r[e],i=(1==y?n<m:n>m)?d:x(n),s=a[e];i==d?null!=s?(c=s,null==l?(g(_,i,w(c)),u=l=o=c):c<l?l=c:c>o&&(o=c)):null===s&&(S=!0):(null!=l&&b(_,d,w(l),w(o),w(u),w(c)),null!=s?(g(_,i,w(c=s)),l=o=u=c):(l=o=null,null===s&&(S=!0)),m=t((d=i)+y))}null!=l&&l!=o&&f!=d&&b(_,d,w(l),w(o),w(u),w(c))}else for(let e=1==y?n:i;e>=n&&e<=i;e+=y){let t=a[e];null===t?S=!0:null!=t&&g(_,x(r[e]),w(t))}let[M,E]=t5(e,l);if(null!=o.fill||0!=M){let t=k.fill=new Path2D(_),a=w(o.fillTo(e,l,o.min,o.max,M)),s=x(r[n]),u=x(r[i]);-1==y&&([u,s]=[s,u]),g(t,u,a),g(t,s,a)}if(!o.spanGaps){let u=[];S&&u.push(...t6(r,a,n,i,y,x,t)),k.gaps=u=o.gaps(e,l,n,i,u),k.clip=t8(u,s.ori,d,h,p,m)}return 0!=E&&(k.band=2==E?[t7(e,l,n,i,_,-1),t7(e,l,n,i,_,1)]:t7(e,l,n,i,_,E)),k})}function lg(e,t,l,n,i,o,r=eb){if(e.length>1){let a=null;for(let s=0,u=1/0;s<e.length;s++)if(void 0!==t[s]){if(null!=a){let t=eo(e[s]-e[a]);t<u&&(u=t,r=eo(l(e[s],n,i,o)-l(e[a],n,i,o)))}a=s}}return r}function lb(e,t,l,n,i,o){let r=e.length;if(r<2)return null;let a=new Path2D;if(l(a,e[0],t[0]),2==r)n(a,e[1],t[1]);else{let l=Array(r),n=Array(r-1),o=Array(r-1),s=Array(r-1);for(let l=0;l<r-1;l++)o[l]=t[l+1]-t[l],s[l]=e[l+1]-e[l],n[l]=o[l]/s[l];l[0]=n[0];for(let e=1;e<r-1;e++)0===n[e]||0===n[e-1]||n[e-1]>0!=n[e]>0?l[e]=0:(l[e]=3*(s[e-1]+s[e])/((2*s[e]+s[e-1])/n[e-1]+(s[e]+2*s[e-1])/n[e]),isFinite(l[e])||(l[e]=0));l[r-1]=n[r-2];for(let n=0;n<r-1;n++)i(a,e[n]+s[n]/3,t[n]+l[n]*s[n]/3,e[n+1]-s[n]/3,t[n+1]-l[n+1]*s[n]/3,e[n+1],t[n+1])}return a}let lv=new Set;function lx(){for(let e of lv)e.syncRect(!0)}k&&(I("resize",S,lx),I("scroll",S,lx,!0),I(y,S,()=>{lj.pxRatio=n}));let lw=lm(),ly=lf();function lk(e,t,l,n){return(n?[e[0],e[1]].concat(e.slice(2)):[e[0]].concat(e.slice(1))).map((e,n)=>l_(e,n,t,l))}function l_(e,t,l,n){return eq({},0==t?l:n,e)}function lS(e,t,l){return null==t?eZ:[t,l]}function lM(e,t,l){return null==t?eZ:J(t,l,.1,!0)}function lE(e,t,l,n){return null==t?eZ:U(t,l,e.scales[n].log,!1)}function lO(e,t,l,n){return null==t?eZ:q(t,l,e.scales[n].log,!1)}function lP(e){let t,l;return[e=e.replace(/(\d+)px/,(e,i)=>(t=ea((l=+i)*n))+"px"),t,l]}function lC(e){e.show&&[e.font,e.labelFont].forEach(e=>{let t=eT(e[2]*n,1);e[0]=e[0].replace(/[0-9.]+px/,t+"px"),e[1]=t})}function lj(e,t,l){let i,d,w,k,M,D,z,R,N,Z,L,$,K,ee,et,el,en,er,ed,ep,ek,eO,eP,ej,eD,eR,eW,eF,eV,e$,eK,eJ,eQ,e0,e1,e2;let e3={mode:Q(e.mode,1)},e4=e3.mode;function e7(e,t,l,n){let i=t.valToPct(e);return n+l*(-1==t.dir?1-i:i)}function e8(e,t,l,n){let i=t.valToPct(e);return n+l*(-1==t.dir?i:1-i)}function e6(e,t,l,n){return 0==t.ori?e7(e,t,l,n):e8(e,t,l,n)}e3.valToPosH=e7,e3.valToPosV=e8;let e9=!1;e3.status=0;let tl=e3.root=j("uplot");null!=e.id&&(tl.id=e.id),E(tl,e.class),e.title&&(j("u-title",tl).textContent=e.title);let tn=C("canvas"),ti=e3.ctx=tn.getContext("2d"),to=j("u-wrap",tl);I("click",to,e=>{e.target===ta&&(eF!=ej||eV!=eD)&&nv.click(e3,e)},!0);let tr=e3.under=j("u-under",to);to.appendChild(tn);let ta=e3.over=j("u-over",to),ts=+Q((e=eU(e)).pxAlign,1),tu=t9(ts);(e.plugins||[]).forEach(t=>{t.opts&&(e=t.opts(e3,e)||e)});let tc=e.ms||.001,tf=e3.series=1==e4?lk(e.series||[],tA,tK,!1):(e.series||[null]).map((e,t)=>0==t?{}:eq({},t$,e)),tw=e3.axes=lk(e.axes||[],tz,tU,!0),t_=e3.scales={},tS=e3.bands=e.bands||[];tS.forEach(e=>{e.fill=ey(e.fill||null),e.dir=Q(e.dir,-1)});let tM=2==e4?tf[1].facets[0].scale:tf[0].scale,tO={axes:function(){for(let e=0;e<tw.length;e++){let t,l,i=tw[e];if(!i.show||!i._show)continue;let o=i.side,r=o%2,a=i.stroke(e3,e),s=0==o||3==o?-1:1,[d,h]=i._found;if(null!=i.label){let c=i.labelGap*s,f=ea((i._lpos+c)*n);ne(i.labelFont[0],a,"center",2==o?"top":u),ti.save(),1==r?(t=l=0,ti.translate(f,ea(lD+lz/2)),ti.rotate((3==o?-ei:ei)/2)):(t=ea(lj+lT/2),l=f);let p=ew(i.label)?i.label(e3,e,d,h):i.label;ti.fillText(p,t,l),ti.restore()}if(0==h)continue;let p=t_[i.scale],m=0==r?lT:lz,g=0==r?lj:lD,b=i._splits,v=2==p.distr?b.map(e=>l4[e]):b,x=2==p.distr?l4[b[1]]-l4[b[0]]:d,w=i.ticks,y=i.border,k=w.show?w.size:0,_=ea(k*n),S=ea((2==i.alignTo?i._size-k-i.gap:i.gap)*n),M=-(i._rotate*ei)/180,E=tu(i._pos*n),O=E+(_+S)*s;l=0==r?O:0,t=1==r?O:0,ne(i.font[0],a,1==i.align?c:2==i.align?f:M>0?c:M<0?f:0==r?"center":3==o?f:c,M||1==r?"middle":2==o?"top":u);let P=i.font[1]*i.lineGap,C=b.map(e=>tu(e6(e,p,m,g))),j=i._values;for(let e=0;e<j.length;e++){let n=j[e];if(null!=n){0==r?t=C[e]:l=C[e];let i=-1==(n=""+n).indexOf("\n")?[n]:n.split(/\n/gm);for(let e=0;e<i.length;e++){let n=i[e];M?(ti.save(),ti.translate(t,l+e*P),ti.rotate(M),ti.fillText(n,0,0),ti.restore()):ti.fillText(n,t,l+e*P)}}}w.show&&ns(C,w.filter(e3,v,e,h,x),r,o,E,_,eT(w.width*n,3),w.stroke(e3,e),w.dash,w.cap);let D=i.grid;D.show&&ns(C,D.filter(e3,v,e,h,x),r,0==r?2:1,0==r?lD:lj,0==r?lz:lT,eT(D.width*n,3),D.stroke(e3,e),D.dash,D.cap),y.show&&ns([E],[1],0==r?1:0,0==r?1:2,1==r?lD:lj,1==r?lz:lT,eT(y.width*n,3),y.stroke(e3,e),y.dash,y.cap)}nV("drawAxes")},series:function(){if(z>0){let e=tf.some(e=>e._focus)&&en!=lG.alpha;e&&(ti.globalAlpha=en=lG.alpha),tf.forEach((e,l)=>{if(l>0&&e.show&&(nn(l,!1),nn(l,!0),null==e._paths)){let n=en;en!=e.alpha&&(ti.globalAlpha=en=e.alpha);let i=2==e4?[0,t[l][0].length-1]:function(e){let t=ex(l2-1,0,z-1),l=ex(l3+1,0,z-1);for(;null==e[t]&&t>0;)t--;for(;null==e[l]&&l<z-1;)l++;return[t,l]}(t[l]);e._paths=e.paths(e3,l,i[0],i[1]),en!=n&&(ti.globalAlpha=en=n)}}),tf.forEach((e,t)=>{if(t>0&&e.show){let l=en;en!=e.alpha&&(ti.globalAlpha=en=e.alpha),null!=e._paths&&ni(t,!1);{let l=null!=e._paths?e._paths.gaps:null,n=e.points.show(e3,t,l2,l3,l),i=e.points.filter(e3,t,n,l);(n||i)&&(e.points._paths=e.points.paths(e3,t,l2,l3,i),ni(t,!0))}en!=l&&(ti.globalAlpha=en=l),nV("drawSeries",t)}}),e&&(ti.globalAlpha=en=1)}}},tP=(e.drawOrder||["axes","series"]).map(e=>tO[e]);function tC(e){let t=3==e.distr?t=>eh(t>0?t:e.clamp(e3,t,e.min,e.max,e.key)):4==e.distr?t=>eg(t,e.asinh):100==e.distr?t=>e.fwd(t):e=>e;return l=>{let n=t(l),{_min:i,_max:o}=e;return(n-i)/(o-i)}}function tj(t){let l=t_[t];if(null==l){let n=(e.scales||eH)[t]||eH;if(null!=n.from){tj(n.from);let e=eq({},t_[n.from],n,{key:t});e.valToPct=tC(e),t_[t]=e}else{(l=t_[t]=eq({},t==tM?tQ:t0,n)).key=t;let e=l.time,i=l.range,o=eI(i);if((t!=tM||2==e4&&!e)&&(o&&(null==i[0]||null==i[1])&&(i={min:null==i[0]?X:{mode:1,hard:i[0],soft:i[0]},max:null==i[1]?X:{mode:1,hard:i[1],soft:i[1]}},o=!1),!o&&eB(i))){let e=i;i=(t,l,n)=>null==l?eZ:J(l,n,e)}l.range=ey(i||(e?lS:t==tM?3==l.distr?lE:4==l.distr?lO:lS:3==l.distr?lE:4==l.distr?lO:lM)),l.auto=ey(!o&&l.auto),l.clamp=ey(l.clamp||tJ),l._min=l._max=null,l.valToPct=tC(l)}}}for(let t in tj("x"),tj("y"),1==e4&&tf.forEach(e=>{tj(e.scale)}),tw.forEach(e=>{tj(e.scale)}),e.scales)tj(t);let tD=t_[tM],tT=tD.distr;0==tD.ori?(E(tl,"u-hz"),i=e7,d=e8):(E(tl,"u-vt"),i=e8,d=e7);let tI={};for(let e in t_){let t=t_[e];(null!=t.min||null!=t.max)&&(tI[e]={min:t.min,max:t.max},t.min=t.max=null)}let tW=e.tzDate||(e=>new Date(ea(e/tc))),tY=e.fmtDate||e5,tL=1==tc?tp(tW):tb(tW),tB=tx(tW,tv(1==tc?th:tg,tY)),tq=ty(tW,tY("{YYYY}-{MM}-{DD} {h}:{mm}{aa}")),tX=[],t1=e3.legend=eq({},tk,e.legend),t3=e3.cursor=eq({},tE,{drag:{y:2==e4}},e.cursor),t5=t1.show,t7=t3.show,t8=t1.markers;t1.idxs=tX,t8.width=ey(t8.width),t8.dash=ey(t8.dash),t8.stroke=ey(t8.stroke),t8.fill=ey(t8.fill);let t6=[],le=[],lt=!1,ll={};if(t1.live){let e=tf[1]?tf[1].values:null;for(let t in D=(lt=null!=e)?e(e3,1,0):{_:0})ll[t]="--"}if(t5){if(w=C("table","u-legend",tl),M=C("tbody",null,w),t1.mount(e3,w),lt){k=C("thead",null,w,M);let e=C("tr",null,k);for(var ln in C("th",null,e),D)C("th",r,e).textContent=ln}else E(w,"u-inline"),t1.live&&E(w,"u-live")}let li={show:!0},lo={show:!1},lr=new Map;function la(e,t,l,n=!0){let i=lr.get(t)||{},o=t3.bind[e](e3,t,l,n);o&&(I(e,t,i[e]=o),lr.set(t,i))}function ls(e,t,l){let n=lr.get(t)||{};for(let l in n)(null==e||l==e)&&(W(l,t,n[l]),delete n[l]);null==e&&lr.delete(t)}let lu=0,lc=0,lf=0,ld=0,lh=0,lp=0,lm=0,lg=0,lb=0,lx=0,lj=0,lD=0,lT=0,lz=0;e3.bbox={};let lA=!1,lR=!1,lH=!1,lN=!1,lZ=!1,lI=!1;function lW(e,t,l){(l||e!=e3.width||t!=e3.height)&&lY(e,t),nu(!1),lH=!0,lR=!0,np()}function lY(e,t){let l,i,o,r;e3.width=lu=lf=e,e3.height=lc=ld=t,lh=lp=0,l=!1,i=!1,o=!1,r=!1,tw.forEach((e,t)=>{if(e.show&&e._show){let{side:t,_size:n}=e,a=n+(null!=e.label?e.labelSize:0);a>0&&(t%2?(lf-=a,3==t?(lh+=a,r=!0):o=!0):(ld-=a,0==t?(lp+=a,l=!0):i=!0))}}),lJ[0]=l,lJ[1]=o,lJ[2]=i,lJ[3]=r,lf-=l1[1]+l1[3],lh+=l1[3],ld-=l1[2]+l1[0],lp+=l1[0],function(){let e=lh+lf,t=lp+ld,l=lh,n=lp;function i(i,o){switch(i){case 1:return(e+=o)-o;case 2:return(t+=o)-o;case 3:return(l-=o)+o;case 0:return(n-=o)+o}}tw.forEach((e,t)=>{if(e.show&&e._show){let t=e.side;e._pos=i(t,e._size),null!=e.label&&(e._lpos=i(t,e.labelSize))}})}();let a=e3.bbox;lj=a.left=eC(lh*n,.5),lD=a.top=eC(lp*n,.5),lT=a.width=eC(lf*n,.5),lz=a.height=eC(ld*n,.5)}if(e3.setSize=function({width:e,height:t}){lW(e,t)},null==t3.dataIdx){let e=t3.hover,l=e.skip=new Set(e.skip??[]);l.add(void 0);let n=e.prox=ey(e.prox),o=e.bias??=0;t3.dataIdx=(e,r,a,s)=>{if(0==r)return a;let u=a,c=n(e,r,a,s)??eb,f=c>=0&&c<eb,d=0==tD.ori?lf:ld,h=t3.left,p=t[0],m=t[r];if(l.has(m[a])){u=null;let e=null,t=null,n;if(0==o||-1==o)for(n=a;null==e&&n-- >0;)l.has(m[n])||(e=n);if(0==o||1==o)for(n=a;null==t&&n++<m.length;)l.has(m[n])||(t=n);if(null!=e||null!=t){if(f){let l=null==e?-1/0:i(p[e],tD,d,0),n=null==t?1/0:i(p[t],tD,d,0),o=h-l,r=n-h;o<=r?o<=c&&(u=e):r<=c&&(u=t)}else u=null==t?e:null==e?t:a-e<=t-a?e:t}}else f&&eo(h-i(p[a],tD,d,0))>c&&(u=null);return u}}let lL=e=>{t3.event=e};t3.idxs=tX,t3._lock=!1;let lB=t3.points;lB.show=ey(lB.show),lB.size=ey(lB.size),lB.stroke=ey(lB.stroke),lB.width=ey(lB.width),lB.fill=ey(lB.fill);let lG=e3.focus=eq({},e.focus||{alpha:.3},t3.focus),lF=lG.prox>=0,lV=lF&&lB.one,lU=[],lq=[],lX=[];function l$(e,t){let l=lB.show(e3,t);if(l instanceof HTMLElement)return E(l,"u-cursor-pt"),E(l,e.class),T(l,-10,-10,lf,ld),ta.insertBefore(l,lU[t]),l}function lK(e,t){if(1==e4||t>0){let t=1==e4&&t_[e.scale].time,l=e.value;e.value=t?eL(l)?ty(tW,tY(l)):l||tq:l||tV,e.label=e.label||(t?"Time":"Value")}if(lV||t>0){e.width=null==e.width?1:e.width,e.paths=e.paths||lw||eS,e.fillTo=ey(e.fillTo||t4),e.pxAlign=+Q(e.pxAlign,ts),e.pxRound=t9(e.pxAlign),e.stroke=ey(e.stroke||null),e.fill=ey(e.fill||null),e._stroke=e._fill=e._paths=e._focus=null;let t=eT((3+2*(ec(1,e.width)||1))*1,3),l=e.points=eq({},{size:t,width:ec(1,.2*t),stroke:e.stroke,space:2*t,paths:ly,_stroke:null,_fill:null},e.points);l.show=ey(l.show),l.filter=ey(l.filter),l.fill=ey(l.fill),l.stroke=ey(l.stroke),l.paths=ey(l.paths),l.pxAlign=e.pxAlign}if(t5){let l=function(e,t){if(0==t&&(lt||!t1.live||2==e4))return eZ;let l=[],n=C("tr","u-series",M,M.childNodes[t]);E(n,e.class),e.show||E(n,o);let i=C("th",null,n);if(t8.show){let e=j("u-marker",i);if(t>0){let l=t8.width(e3,t);l&&(e.style.border=l+"px "+t8.dash(e3,t)+" "+t8.stroke(e3,t)),e.style.background=t8.fill(e3,t)}}let a=j(r,i);for(var s in e.label instanceof HTMLElement?a.appendChild(e.label):a.textContent=e.label,t>0&&(t8.show||(a.style.color=e.width>0?t8.stroke(e3,t):t8.fill(e3,t)),la("click",i,t=>{if(t3._lock)return;lL(t);let l=tf.indexOf(e);if((t.ctrlKey||t.metaKey)!=t1.isolate){let e=tf.some((e,t)=>t>0&&t!=l&&e.show);tf.forEach((t,n)=>{n>0&&nM(n,e?n==l?li:lo:li,!0,nq.setSeries)})}else nM(l,{show:!e.show},!0,nq.setSeries)},!1),lF&&la(b,i,t=>{t3._lock||(lL(t),nM(tf.indexOf(e),nE,!0,nq.setSeries))},!1)),D){let e=C("td","u-value",n);e.textContent="--",l.push(e)}return[n,l]}(e,t);t6.splice(t,0,l[0]),le.splice(t,0,l[1]),t1.values.push(null)}if(t7){tX.splice(t,0,null);let l=null;lV?0==t&&(l=l$(e,t)):t>0&&(l=l$(e,t)),lU.splice(t,0,l),lq.splice(t,0,0),lX.splice(t,0,0)}nV("addSeries",t)}e3.addSeries=function(e,t){t=null==t?tf.length:t,e=1==e4?l_(e,t,tA,tK):l_(e,t,{},t$),tf.splice(t,0,e),lK(tf[t],t)},e3.delSeries=function(e){if(tf.splice(e,1),t5){t1.values.splice(e,1),le.splice(e,1);let t=t6.splice(e,1)[0];ls(null,t.firstChild),t.remove()}t7&&(tX.splice(e,1),lU.splice(e,1)[0].remove(),lq.splice(e,1),lX.splice(e,1)),nV("delSeries",e)};let lJ=[!1,!1,!1,!1];function lQ(e,t,l,n){let[i,o,r,a]=l,s=t%2,u=0;return 0==s&&(a||o)&&(u=(0!=t||i)&&(2!=t||r)?0:ea(tz.size/3)),1==s&&(i||r)&&(u=(1!=t||o)&&(3!=t||a)?0:ea(tU.size/2)),u}let l0=e3.padding=(e.padding||[lQ,lQ,lQ,lQ]).map(e=>ey(Q(e,lQ))),l1=e3._padding=l0.map((e,t)=>e(e3,t,lJ,0)),l2=null,l3=null,l5=1==e4?tf[0].idxs:null,l4=null,l7=!1;function l8(e,l){if(t=null==e?[]:e,e3.data=e3._data=t,2==e4){z=0;for(let e=1;e<tf.length;e++)z+=t[e][0].length}else{0==t.length&&(e3.data=e3._data=t=[[]]),z=(l4=t[0]).length;let e=t;if(2==tT){let l=(e=t.slice())[0]=Array(z);for(let e=0;e<z;e++)l[e]=e}e3._data=t=e}nu(!0),nV("setData"),2==tT&&(lH=!0),!1!==l&&(tD.auto(e3,l7)?l6():nS(tM,tD.min,tD.max),lN=lN||t3.left>=0,lI=!0,np())}function l6(){let e,l;l7=!0,1==e4&&(z>0?(l2=l5[0]=0,l3=l5[1]=z-1,e=t[0][l2],l=t[0][l3],2==tT?(e=l2,l=l3):e==l&&(3==tT?[e,l]=U(e,e,tD.log,!1):4==tT?[e,l]=q(e,e,tD.log,!1):tD.time?l=e+ea(86400/tc):[e,l]=J(e,l,.1,!0))):(l2=l5[0]=e=null,l3=l5[1]=l=null)),nS(tM,e,l)}function l9(e,t,l,n,i,o){e??=h,l??=eN,n??="butt",i??=h,o??="round",e!=R&&(ti.strokeStyle=R=e),i!=N&&(ti.fillStyle=N=i),t!=Z&&(ti.lineWidth=Z=t),o!=$&&(ti.lineJoin=$=o),n!=K&&(ti.lineCap=K=n),l!=L&&ti.setLineDash(L=l)}function ne(e,t,l,n){t!=N&&(ti.fillStyle=N=t),e!=ee&&(ti.font=ee=e),l!=et&&(ti.textAlign=et=l),n!=el&&(ti.textBaseline=el=n)}function nt(e,t,l,n,i=0){if(n.length>0&&e.auto(e3,l7)&&(null==t||null==t.min)){let t=Q(l2,0),o=Q(l3,n.length-1),r=null==l.min?function(e,t,l,n=0,i=!1){let o=i?G:B;[t,l]=(i?V:F)(e,t,l);let r=e[t],a=e[t];if(t>-1){if(1==n)r=e[t],a=e[l];else if(-1==n)r=e[l],a=e[t];else for(let n=t;n<=l;n++){let t=e[n];o(t)&&(t<r?r=t:t>a&&(a=t))}}return[r??eb,a??-eb]}(n,t,o,i,3==e.distr):[l.min,l.max];e.min=eu(e.min,l.min=r[0]),e.max=ec(e.max,l.max=r[1])}}e3.setData=l8;let nl={min:null,max:null};function nn(e,t){let l=t?tf[e].points:tf[e];l._stroke=l.stroke(e3,e),l._fill=l.fill(e3,e)}function ni(e,l){var i,o,r,a,s;let u,c=l?tf[e].points:tf[e],{stroke:f,fill:d,clip:h,flags:p,_stroke:m=c._stroke,_fill:g=c._fill,_width:b=c.width}=c._paths;b=eT(b*n,3);let v=null,x=b%2/2;l&&null==g&&(g=b>0?"#fff":m);let w=1==c.pxAlign&&x>0;if(w&&ti.translate(x,x),!l){let e=lj-b/2,t=lD-b/2,l=lT+b,n=lz+b;(v=new Path2D).rect(e,t,l,n)}l?no(m,b,c.dash,c.cap,g,f,d,p,h):(i=b,o=c.dash,r=c.cap,a=g,s=v,u=!1,0!=p&&tS.forEach((l,n)=>{if(l.series[0]==e){let e,c=tf[l.series[1]],g=t[l.series[1]],b=(c._paths||eH).band;eI(b)&&(b=1==l.dir?b[0]:b[1]);let v=null;c.show&&b&&function(e,t,l){for(t=Q(t,0),l=Q(l,e.length-1);t<=l;){if(null!=e[t])return!0;t++}return!1}(g,l2,l3)?(v=l.fill(e3,n)||a,e=c._paths.clip):b=null,no(m,i,o,r,v,f,d,p,s,h,e,b),u=!0}}),u||no(m,i,o,r,a,f,d,p,s,h)),w&&ti.translate(-x,-x)}function no(e,t,l,n,i,o,r,a,s,u,c,f){l9(e,t,l,n,i),(s||u||f)&&(ti.save(),s&&ti.clip(s),u&&ti.clip(u)),f?(3&a)==3?(ti.clip(f),c&&ti.clip(c),na(i,r),nr(e,o,t)):2&a?(na(i,r),ti.clip(f),nr(e,o,t)):1&a&&(ti.save(),ti.clip(f),c&&ti.clip(c),na(i,r),ti.restore(),nr(e,o,t)):(na(i,r),nr(e,o,t)),(s||u||f)&&ti.restore()}function nr(e,t,l){l>0&&(t instanceof Map?t.forEach((e,t)=>{ti.strokeStyle=R=t,ti.stroke(e)}):null!=t&&e&&ti.stroke(t))}function na(e,t){t instanceof Map?t.forEach((e,t)=>{ti.fillStyle=N=t,ti.fill(e)}):null!=t&&e&&ti.fill(t)}function ns(e,t,l,n,i,o,r,a,s,u){let c=r%2/2;1==ts&&ti.translate(c,c),l9(a,r,s,u,a),ti.beginPath();let f,d,h,p,m=i+(0==n||3==n?-o:o);0==l?(d=i,p=m):(f=i,h=m);for(let n=0;n<e.length;n++)null!=t[n]&&(0==l?f=h=e[n]:d=p=e[n],ti.moveTo(f,d),ti.lineTo(h,p));ti.stroke(),1==ts&&ti.translate(-c,-c)}function nu(e){tf.forEach((t,l)=>{l>0&&(t._paths=null,e&&(1==e4?(t.min=null,t.max=null):t.facets.forEach(e=>{e.min=null,e.max=null})))})}let nc=!1,nf=!1,nd=[];function nh(){nf=!1;for(let e=0;e<nd.length;e++)nV(...nd[e]);nd.length=0}function np(){nc||(eX(nm),nc=!0)}function nm(){if(lA&&(function(){for(let e in t_){let t=t_[e];null==tI[e]&&(null==t.min||null!=tI[tM]&&t.auto(e3,l7))&&(tI[e]=nl)}for(let e in t_){let t=t_[e];null==tI[e]&&null!=t.from&&null!=tI[t.from]&&(tI[e]=nl)}null!=tI[tM]&&nu(!0);let e={};for(let t in tI){let l=tI[t];if(null!=l){let n=e[t]=eU(t_[t],eG);if(null!=l.min)eq(n,l);else if(t!=tM||2==e4){if(0==z&&null==n.from){let e=n.range(e3,null,null,t);n.min=e[0],n.max=e[1]}else n.min=eb,n.max=-eb}}}if(z>0)for(let l in tf.forEach((l,n)=>{if(1==e4){let i=l.scale,o=tI[i];if(null==o)return;let r=e[i];if(0==n){let e=r.range(e3,r.min,r.max,i);r.min=e[0],r.max=e[1],l2=Y(r.min,t[0]),(l3=Y(r.max,t[0]))-l2>1&&(t[0][l2]<r.min&&l2++,t[0][l3]>r.max&&l3--),l.min=l4[l2],l.max=l4[l3]}else l.show&&l.auto&&nt(r,o,l,t[n],l.sorted);l.idxs[0]=l2,l.idxs[1]=l3}else if(n>0&&l.show&&l.auto){let[i,o]=l.facets,r=i.scale,a=o.scale,[s,u]=t[n],c=e[r],f=e[a];null!=c&&nt(c,tI[r],i,s,i.sorted),null!=f&&nt(f,tI[a],o,u,o.sorted),l.min=o.min,l.max=o.max}}),e){let t=e[l],n=tI[l];if(null==t.from&&(null==n||null==n.min)){let e=t.range(e3,t.min==eb?null:t.min,t.max==-eb?null:t.max,l);t.min=e[0],t.max=e[1]}}for(let t in e){let l=e[t];if(null!=l.from){let n=e[l.from];if(null==n.min)l.min=l.max=null;else{let e=l.range(e3,n.min,n.max,t);l.min=e[0],l.max=e[1]}}}let l={},n=!1;for(let t in e){let i=e[t],o=t_[t];if(o.min!=i.min||o.max!=i.max){o.min=i.min,o.max=i.max;let e=o.distr;o._min=3==e?eh(o.min):4==e?eg(o.min,o.asinh):100==e?o.fwd(o.min):o.min,o._max=3==e?eh(o.max):4==e?eg(o.max,o.asinh):100==e?o.fwd(o.max):o.max,l[t]=n=!0}}if(n){for(let e in tf.forEach((e,t)=>{2==e4?t>0&&l.y&&(e._paths=null):l[e.scale]&&(e._paths=null)}),l)lH=!0,nV("setScale",e);t7&&t3.left>=0&&(lN=lI=!0)}for(let e in tI)tI[e]=null}(),lA=!1),lH&&(function(){let e=!1,t=0;for(;!e;){let l=function(e){let t=!0;return tw.forEach((l,n)=>{if(!l.show)return;let i=t_[l.scale];if(null==i.min){l._show&&(t=!1,l._show=!1,nu(!1));return}l._show||(t=!1,l._show=!0,nu(!1));let o=l.side,{min:r,max:a}=i,[s,u]=function(e,t,l,n){let i,o=tw[e];if(n<=0)i=[0,0];else{let r=o._space=o.space(e3,e,t,l,n),a=o._incrs=o.incrs(e3,e,t,l,n,r);i=function(e,t,l,n,i){let o=ec(ev(e),ev(t)),r=t-e,a=Y(i/n*r,l);do{let e=l[a],t=n*e/r;if(t>=i&&o+(e<5?ez.get(e):0)<=17)return[e,t]}while(++a<l.length);return[0,0]}(t,l,a,n,r)}return o._found=i}(n,r,a,0==o%2?lf:ld);if(0==u)return;let c=2==i.distr,f=l._splits=l.splits(e3,n,r,a,s,u,c),d=2==i.distr?f.map(e=>l4[e]):f,h=2==i.distr?l4[f[1]]-l4[f[0]]:s,p=l._values=l.values(e3,l.filter(e3,d,n,u,h),n,u,h);l._rotate=2==o?l.rotate(e3,p,n,u):0;let m=l._size;l._size=es(l.size(e3,p,n,e)),null!=m&&l._size!=m&&(t=!1)}),t}(++t),n=function(e){let t=!0;return l0.forEach((l,n)=>{let i=l(e3,n,lJ,e);i!=l1[n]&&(t=!1),l1[n]=i}),t}(t);(e=3==t||l&&n)||(lY(e3.width,e3.height),lR=!0)}}(),lH=!1),lR){if(P(tr,c,lh),P(tr,"top",lp),P(tr,a,lf),P(tr,s,ld),P(ta,c,lh),P(ta,"top",lp),P(ta,a,lf),P(ta,s,ld),P(to,a,lu),P(to,s,lc),tn.width=ea(lu*n),tn.height=ea(lc*n),tw.forEach(({_el:e,_show:t,_size:l,_pos:n,side:i})=>{if(null!=e){if(t){let t=3===i||0===i?l:0,r=i%2==1;P(e,r?"left":"top",n-t),P(e,r?"width":"height",l),P(e,r?"top":"left",r?lp:lh),P(e,r?"height":"width",r?ld:lf),O(e,o)}else E(e,o)}}),R=N=Z=$=K=ee=et=el=L=null,en=1,nR(!0),lh!=lm||lp!=lg||lf!=lb||ld!=lx){nu(!1);let e=lf/lb,t=ld/lx;if(t7&&!lN&&t3.left>=0){t3.left*=e,t3.top*=t,ep&&T(ep,ea(t3.left),0,lf,ld),ek&&T(ek,0,ea(t3.top),lf,ld);for(let l=0;l<lU.length;l++){let n=lU[l];null!=n&&(lq[l]*=e,lX[l]*=t,T(n,es(lq[l]),es(lX[l]),lf,ld))}}if(ny.show&&!lZ&&ny.left>=0&&ny.width>0)for(let l in ny.left*=e,ny.width*=e,ny.top*=t,ny.height*=t,nZ)P(nk,l,ny[l]);lm=lh,lg=lp,lb=lf,lx=ld}nV("setSize"),lR=!1}lu>0&&lc>0&&(ti.clearRect(0,0,tn.width,tn.height),nV("drawClear"),tP.forEach(e=>e()),nV("draw")),ny.show&&lZ&&(n_(ny),lZ=!1),t7&&lN&&(nz(null,!0,!1),lN=!1),t1.show&&t1.live&&lI&&(nT(),lI=!1),e9||(e9=!0,e3.status=1,nV("ready")),l7=!1,nc=!1}function ng(e,l){let n=t_[e];if(null==n.from){if(0==z){let t=n.range(e3,l.min,l.max,e);l.min=t[0],l.max=t[1]}if(l.min>l.max){let e=l.min;l.min=l.max,l.max=e}if(z>1&&null!=l.min&&null!=l.max&&l.max-l.min<1e-16)return;e==tM&&2==n.distr&&z>0&&(l.min=Y(l.min,t[0]),l.max=Y(l.max,t[0]),l.min==l.max&&l.max++),tI[e]=l,lA=!0,np()}}e3.batch=function(e,t=!1){nc=!0,nf=t,e(e3),nm(),t&&nd.length>0&&queueMicrotask(nh)},e3.redraw=(e,t)=>{lH=t||!1,!1!==e?nS(tM,tD.min,tD.max):np()},e3.setScale=ng;let nb=!1,nv=t3.drag,nx=nv.x,nw=nv.y;t7&&(t3.x&&(er=j("u-cursor-x",ta)),t3.y&&(ed=j("u-cursor-y",ta)),0==tD.ori?(ep=er,ek=ed):(ep=ed,ek=er),eF=t3.left,eV=t3.top);let ny=e3.select=eq({show:!0,over:!0,left:0,width:0,top:0,height:0},e.select),nk=ny.show?j("u-select",ny.over?ta:tr):null;function n_(e,t){if(ny.show){for(let t in e)ny[t]=e[t],t in nZ&&P(nk,t,e[t]);!1!==t&&nV("setSelect")}}function nS(e,t,l){ng(e,{min:t,max:l})}function nM(e,t,l,n){null!=t.focus&&function(e){if(e!=eJ){let t=null==e,l=1!=lG.alpha;tf.forEach((n,i)=>{if(1==e4||i>0){var o;let r=t||0==i||i==e;n._focus=t?null:r,l&&(o=r?1:lG.alpha,tf[i].alpha=o,t7&&null!=lU[i]&&(lU[i].style.opacity=o),t5&&t6[i]&&(t6[i].style.opacity=o))}}),eJ=e,l&&np()}}(e),null!=t.show&&tf.forEach((l,n)=>{n>0&&(e==n||null==e)&&(l.show=t.show,function(e){if(tf[e].show)t5&&O(t6[e],o);else if(t5&&E(t6[e],o),t7){let t=lV?lU[0]:lU[e];null!=t&&T(t,-10,-10,lf,ld)}}(n),2==e4?(nS(l.facets[0].scale,null,null),nS(l.facets[1].scale,null,null)):nS(l.scale,null,null),np())}),!1!==l&&nV("setSeries",e,t),n&&nK("setSeries",e3,e,t)}e3.setSelect=n_,e3.setSeries=nM,e3.addBand=function(e,t){e.fill=ey(e.fill||null),e.dir=Q(e.dir,-1),t=null==t?tS.length:t,tS.splice(t,0,e)},e3.setBand=function(e,t){eq(tS[e],t)},e3.delBand=function(e){null==e?tS.length=0:tS.splice(e,1)};let nE={focus:!0};function nO(e,t,l){let i=t_[t];l&&(e=e/n-(1==i.ori?lp:lh));let o=lf;1==i.ori&&(e=(o=ld)-e),-1==i.dir&&(e=o-e);let r=i._min,a=r+(i._max-r)*(e/o),s=i.distr;return 3==s?ef(10,a):4==s?em(a,i.asinh):100==s?i.bwd(a):a}function nP(e,t){P(nk,c,ny.left=e),P(nk,a,ny.width=t)}function nC(e,t){P(nk,"top",ny.top=e),P(nk,s,ny.height=t)}t5&&lF&&la(v,w,e=>{t3._lock||(lL(e),null!=eJ&&nM(null,nE,!0,nq.setSeries))}),e3.valToIdx=e=>Y(e,t[0]),e3.posToIdx=function(e,l){return Y(nO(e,tM,l),t[0],l2,l3)},e3.posToVal=nO,e3.valToPos=(e,t,l)=>0==t_[t].ori?e7(e,t_[t],l?lT:lf,l?lj:0):e8(e,t_[t],l?lz:ld,l?lD:0),e3.setCursor=(e,t,l)=>{eF=e.left,eV=e.top,nz(null,t,l)};let nj=0==tD.ori?nP:nC,nD=1==tD.ori?nP:nC;function nT(e,l){if(null!=e&&(e.idxs?e.idxs.forEach((e,t)=>{tX[t]=e}):eY(e.idx)||tX.fill(e.idx),t1.idx=tX[0]),t5&&t1.live){for(let e=0;e<tf.length;e++)(e>0||1==e4&&!lt)&&function(e,l){let n,i=tf[e],o=0==e&&2==tT?l4:t[e];n=lt?i.values(e3,e,l)??ll:null==(n=i.value(e3,null==l?null:o[l],e,l))?ll:{_:n},t1.values[e]=n}(e,tX[e]);!function(){if(t5&&t1.live)for(let e=2==e4?1:0;e<tf.length;e++){if(0==e&&lt)continue;let t=t1.values[e],l=0;for(let n in t)le[e][l++].firstChild.nodeValue=t[n]}}()}lI=!1,!1!==l&&nV("setLegend")}function nz(e,l,n){let o;eR=eF,eW=eV,[eF,eV]=t3.move(e3,eF,eV),t3.left=eF,t3.top=eV,t7&&(ep&&T(ep,ea(eF),0,lf,ld),ek&&T(ek,0,ea(eV),lf,ld));let r=l2>l3;e$=eb,eK=null;let a=0==tD.ori?lf:ld,s=1==tD.ori?lf:ld;if(eF<0||0==z||r){o=t3.idx=null;for(let e=0;e<tf.length;e++){let t=lU[e];null!=t&&T(t,-10,-10,lf,ld)}lF&&nM(null,nE,!0,null==e&&nq.setSeries),t1.live&&(tX.fill(o),lI=!0)}else{let e,l;1==e4&&(e=nO(0==tD.ori?eF:eV,tM),o=t3.idx=Y(e,t[0],l2,l3),l=i(t[0][o],tD,a,0));let n=-10,r=-10,u=0,c=0,f=!0,h="",p="";for(let m=2==e4?1:0;m<tf.length;m++){let g=tf[m],b=tX[m],v=null==b?null:1==e4?t[m][b]:t[m][1][b],x=t3.dataIdx(e3,m,o,e),w=null==x?null:1==e4?t[m][x]:t[m][1][x];if(lI=lI||w!=v||x!=b,tX[m]=x,m>0&&g.show){let e=null==x?-10:x==o?l:i(1==e4?t[0][x]:t[m][0][x],tD,a,0),b=null==w?-10:d(w,1==e4?t_[g.scale]:t_[g.facets[1].scale],s,0);if(lF&&null!=w){let e=1==tD.ori?eF:eV,t=eo(lG.dist(e3,m,x,b,e));if(t<e$){let l=lG.bias;if(0!=l){let n=nO(e,g.scale),i=n>=0?1:-1;i==(w>=0?1:-1)&&(1==i?1==l?w>=n:w<=n:1==l?w<=n:w>=n)&&(e$=t,eK=m)}else e$=t,eK=m}}if(lI||lV){let t,l;0==tD.ori?(t=e,l=b):(t=b,l=e);let i,o,a,s,d,g,v=!0,x=lB.bbox;if(null!=x){v=!1;let e=x(e3,m);a=e.left,s=e.top,i=e.width,o=e.height}else a=t,s=l,i=o=lB.size(e3,m);if(g=lB.fill(e3,m),d=lB.stroke(e3,m),lV)m==eK&&e$<=lG.prox&&(n=a,r=s,u=i,c=o,f=v,h=g,p=d);else{let e=lU[m];null!=e&&(lq[m]=a,lX[m]=s,H(e,i,o,v),A(e,g,d),T(e,es(a),es(s),lf,ld))}}}}if(lV){let e=lG.prox,t=null==eJ?e$<=e:e$>e||eK!=eJ;if(lI||t){let e=lU[0];null!=e&&(lq[0]=n,lX[0]=r,H(e,u,c,f),A(e,h,p),T(e,es(n),es(r),lf,ld))}}}if(ny.show&&nb){if(null!=e){let[t,l]=nq.scales,[n,o]=nq.match,[r,u]=e.cursor.sync.scales,c=e.cursor.drag;if(nx=c._x,nw=c._y,nx||nw){let c,f,h,p,m,{left:g,top:b,width:v,height:x}=e.select,w=e.scales[r].ori,y=e.posToVal,k=null!=t&&n(t,r),_=null!=l&&o(l,u);k&&nx?(0==w?(c=g,f=v):(c=b,f=x),h=t_[t],nj(eu(p=i(y(c,r),h,a,0),m=i(y(c+f,r),h,a,0)),eo(m-p))):nj(0,a),_&&nw?(1==w?(c=g,f=v):(c=b,f=x),h=t_[l],nD(eu(p=d(y(c,u),h,s,0),m=d(y(c+f,u),h,s,0)),eo(m-p))):nD(0,s)}else nI()}else{let e,t,l=eo(eR-eO),n=eo(eW-eP);if(1==tD.ori){let e=l;l=n,n=e}nx=nv.x&&l>=nv.dist,nw=nv.y&&n>=nv.dist;let i=nv.uni;null!=i?nx&&nw&&(nx=l>=i,nw=n>=i,nx||nw||(n>l?nw=!0:nx=!0)):nv.x&&nv.y&&(nx||nw)&&(nx=nw=!0),nx&&(0==tD.ori?(e=ej,t=eF):(e=eD,t=eV),nj(eu(e,t),eo(t-e)),nw||nD(0,s)),nw&&(1==tD.ori?(e=ej,t=eF):(e=eD,t=eV),nD(eu(e,t),eo(t-e)),nx||nj(0,a)),nx||nw||(nj(0,0),nD(0,0))}}if(nv._x=nx,nv._y=nw,null==e){if(n){if(null!=nX){let[e,t]=nq.scales;nq.values[0]=null!=e?nO(0==tD.ori?eF:eV,e):null,nq.values[1]=null!=t?nO(1==tD.ori?eF:eV,t):null}nK(p,e3,eF,eV,lf,ld,o)}if(lF){let e=n&&nq.setSeries,t=lG.prox;null==eJ?e$<=t&&nM(eK,nE,!0,e):e$>t?nM(null,nE,!0,e):eK!=eJ&&nM(eK,nE,!0,e)}}lI&&(t1.idx=o,nT()),!1!==l&&nV("setCursor")}e3.setLegend=nT;let nA=null;function nR(e=!1){e?nA=null:nV("syncRect",nA=ta.getBoundingClientRect())}function nH(e,t,l,n,i,o,r){!t3._lock&&(nb&&null!=e&&0==e.movementX&&0==e.movementY||(nN(e,t,l,n,i,o,r,!1,null!=e),null!=e?nz(null,!0,!0):nz(t,!0,!1)))}function nN(e,t,l,n,i,o,r,a,s){if(null==nA&&nR(!1),lL(e),null!=e)l=e.clientX-nA.left,n=e.clientY-nA.top;else{if(l<0||n<0){eF=-10,eV=-10;return}let[e,r]=nq.scales,a=t.cursor.sync,[s,u]=a.values,[c,f]=a.scales,[d,h]=nq.match,p=t.axes[0].side%2==1,m=0==tD.ori?lf:ld,g=1==tD.ori?lf:ld,b=p?n:l,v=p?l:n;if(l=null!=c?d(e,c)?e6(s,t_[e],m,0):-10:b/(p?o:i)*m,n=null!=f?h(r,f)?e6(u,t_[r],g,0):-10:v/(p?i:o)*g,1==tD.ori){let e=l;l=n,n=e}}s&&(null==t||t.cursor.event.type==p)&&((l<=1||l>=lf-1)&&(l=eC(l,lf)),(n<=1||n>=ld-1)&&(n=eC(n,ld))),a?(eO=l,eP=n,[ej,eD]=t3.move(e3,l,n)):(eF=l,eV=n)}Object.defineProperty(e3,"rect",{get:()=>(null==nA&&nR(!1),nA)});let nZ={width:0,height:0,left:0,top:0};function nI(){n_(nZ,!1)}function nW(e,t,l,n,i,o,r){nb=!0,nx=nw=nv._x=nv._y=!1,nN(e,t,l,n,i,o,r,!0,!1),null!=e&&(la(g,_,nY,!1),nK(m,e3,ej,eD,lf,ld,null));let{left:a,top:s,width:u,height:c}=ny;eQ=a,e0=s,e1=u,e2=c}function nY(e,t,l,n,i,o,r){nb=nv._x=nv._y=!1,nN(e,t,l,n,i,o,r,!1,!0);let{left:a,top:s,width:u,height:c}=ny,f=u>0||c>0,d=eQ!=a||e0!=s||e1!=u||e2!=c;if(f&&d&&n_(ny),nv.setScale&&f&&d){let e=a,t=u,l=s,n=c;if(1==tD.ori&&(e=s,t=c,l=a,n=u),nx&&nS(tM,nO(e,tM),nO(e+t,tM)),nw)for(let e in t_){let t=t_[e];e!=tM&&null==t.from&&t.min!=eb&&nS(e,nO(l+n,e),nO(l,e))}nI()}else t3.lock&&(t3._lock=!t3._lock,nz(t,!0,null!=e));null!=e&&(ls(g,_),nK(g,e3,eF,eV,lf,ld,null))}function nL(e,t,l,n,i,o,r){t3._lock||(lL(e),l6(),nI(),null!=e&&nK(x,e3,eF,eV,lf,ld,null))}function nB(){tw.forEach(lC),lW(e3.width,e3.height,!0)}I(y,S,nB);let nG={};nG.mousedown=nW,nG.mousemove=nH,nG.mouseup=nY,nG.dblclick=nL,nG.setSeries=(e,t,l,n)=>{-1!=(l=(0,nq.match[2])(e3,t,l))&&nM(l,n,!0,!1)},t7&&(la(m,ta,nW),la(p,ta,nH),la(b,ta,e=>{lL(e),nR(!1)}),la(v,ta,function(e,t,l,n,i,o,r){if(t3._lock)return;lL(e);let a=nb;if(nb){let e,t,l=!0,n=!0;0==tD.ori?(e=nx,t=nw):(e=nw,t=nx),e&&t&&(l=eF<=10||eF>=lf-10,n=eV<=10||eV>=ld-10),e&&l&&(eF=eF<ej?0:lf),t&&n&&(eV=eV<eD?0:ld),nz(null,!0,!0),nb=!1}eF=-10,eV=-10,tX.fill(null),nz(null,!0,!0),a&&(nb=a)}),la(x,ta,nL),lv.add(e3),e3.syncRect=nR);let nF=e3.hooks=e.hooks||{};function nV(e,t,l){nf?nd.push([e,t,l]):e in nF&&nF[e].forEach(e=>{e.call(null,e3,t,l)})}(e.plugins||[]).forEach(e=>{for(let t in e.hooks)nF[t]=(nF[t]||[]).concat(e.hooks[t])});let nU=(e,t,l)=>l,nq=eq({key:null,setSeries:!1,filters:{pub:eM,sub:eM},scales:[tM,tf[1]?tf[1].scale:null],match:[eE,eE,nU],values:[null,null]},t3.sync);2==nq.match.length&&nq.match.push(nU),t3.sync=nq;let nX=nq.key,n$=t2(nX);function nK(e,t,l,n,i,o,r){nq.filters.pub(e,t,l,n,i,o,r)&&n$.pub(e,t,l,n,i,o,r)}function nJ(){nV("init",e,t),l8(t||e.data,!1),tI[tM]?ng(tM,tI[tM]):l6(),lZ=ny.show&&(ny.width>0||ny.height>0),lN=lI=!0,lW(e.width,e.height)}return n$.sub(e3),e3.pub=function(e,t,l,n,i,o,r){nq.filters.sub(e,t,l,n,i,o,r)&&nG[e](null,t,l,n,i,o,r)},e3.destroy=function(){n$.unsub(e3),lv.delete(e3),lr.clear(),W(y,S,nB),tl.remove(),w?.remove(),nV("destroy")},tf.forEach(lK),tw.forEach(function(e,t){if(e._show=e.show,e.show){let l,n=e.side%2,i=t_[e.scale];null==i&&(e.scale=n?tf[1].scale:tM,i=t_[e.scale]);let o=i.time;e.size=ey(e.size),e.space=ey(e.space),e.rotate=ey(e.rotate),eI(e.incrs)&&e.incrs.forEach(e=>{ez.has(e)||ez.set(e,eA(e))}),e.incrs=ey(e.incrs||(2==i.distr?te:o?1==tc?td:tm:tt)),e.splits=ey(e.splits||(o&&1==i.distr?tL:3==i.distr?tN:4==i.distr?tZ:tH)),e.stroke=ey(e.stroke),e.grid.stroke=ey(e.grid.stroke),e.ticks.stroke=ey(e.ticks.stroke),e.border.stroke=ey(e.border.stroke);let r=e.values;e.values=eI(r)&&!eI(r[0])?ey(r):o?eI(r)?tx(tW,tv(r,tY)):eL(r)?(l=e5(r),(e,t,n,i,o)=>t.map(e=>l(tW(e)))):r||tB:r||tR,e.filter=ey(e.filter||(i.distr>=3&&10==i.log?tG:3==i.distr&&2==i.log?tF:e_)),e.font=lP(e.font),e.labelFont=lP(e.labelFont),e._size=e.size(e3,null,t,0),e._space=e._rotate=e._incrs=e._found=e._splits=e._values=null,e._size>0&&(lJ[t]=!0,e._el=j("u-axis",to))}}),l?l instanceof HTMLElement?(l.appendChild(tl),nJ()):l(e3,nJ):nJ(),e3}lj.assign=eq,lj.fmtNum=el,lj.rangeNum=J,lj.rangeLog=U,lj.rangeAsinh=q,lj.orient=t3,lj.pxRatio=n,lj.join=function(e,t){if(function(e){let t=e[0][0],l=t.length;for(let n=1;n<e.length;n++){let i=e[n][0];if(i.length!=l)return!1;if(i!=t){for(let e=0;e<l;e++)if(i[e]!=t[e])return!1}}return!0}(e)){let t=e[0].slice();for(let l=1;l<e.length;l++)t.push(...e[l].slice(1));return!function(e,t=100){let l=e.length;if(l<=1)return!0;let n=0,i=l-1;for(;n<=i&&null==e[n];)n++;for(;i>=n&&null==e[i];)i--;if(i<=n)return!0;let o=ec(1,er((i-n+1)/t));for(let t=e[n],l=n+o;l<=i;l+=o){let n=e[l];if(null!=n){if(n<=t)return!1;t=n}}return!0}(t[0])&&(t=function(e){let t=e[0],l=t.length,n=Array(l);for(let e=0;e<n.length;e++)n[e]=e;n.sort((e,l)=>t[e]-t[l]);let i=[];for(let t=0;t<e.length;t++){let o=e[t],r=Array(l);for(let e=0;e<l;e++)r[e]=o[n[e]];i.push(r)}return i}(t)),t}let l=new Set;for(let t=0;t<e.length;t++){let n=e[t][0],i=n.length;for(let e=0;e<i;e++)l.add(n[e])}let n=[Array.from(l).sort((e,t)=>e-t)],i=n[0].length,o=new Map;for(let e=0;e<i;e++)o.set(n[0][e],e);for(let l=0;l<e.length;l++){let r=e[l],a=r[0];for(let e=1;e<r.length;e++){let s=r[e],u=Array(i).fill(void 0),c=t?t[l][e]:1,f=[];for(let e=0;e<s.length;e++){let t=s[e],l=o.get(a[e]);null===t?0!=c&&(u[l]=t,2==c&&f.push(l)):u[l]=t}(function(e,t,l){for(let n=0,i,o=-1;n<t.length;n++){let r=t[n];if(r>o){for(i=r-1;i>=0&&null==e[i];)e[i--]=null;for(i=r+1;i<l&&null==e[i];)e[o=i++]=null}}})(u,f,i),n.push(u)}}return n},lj.fmtDate=e5,lj.tzDate=function(e,t){let l;return"UTC"==t||"Etc/UTC"==t?l=new Date(+e+6e4*e.getTimezoneOffset()):t==e4?l=e:(l=new Date(e.toLocaleString("en-US",{timeZone:t}))).setMilliseconds(e.getMilliseconds()),l},lj.sync=t2;{lj.addGap=function(e,t,l){let n=e[e.length-1];n&&n[0]==t?n[1]=l:e.push([t,l])},lj.clipGaps=t8;let e=lj.paths={points:lf};e.linear=lm,e.stepped=function(e){let t=Q(e.align,1),l=Q(e.ascDesc,!1),i=Q(e.alignGaps,0),o=Q(e.extend,!1);return(e,r,a,s)=>t3(e,r,(u,c,f,d,h,p,m,g,b,v,x)=>{[a,s]=F(f,a,s);let w=u.pxRound,{left:y,width:k}=e.bbox,_=e=>w(p(e,d,v,g)),S=e=>w(m(e,h,x,b)),M=0==d.ori?ln:li,E={stroke:new Path2D,fill:null,clip:null,band:null,gaps:null,flags:1},O=E.stroke,P=d.dir*(0==d.ori?1:-1),C=S(f[1==P?a:s]),j=_(c[1==P?a:s]),D=j,T=j;o&&-1==t&&M(O,T=y,C),M(O,j,C);for(let e=1==P?a:s;e>=a&&e<=s;e+=P){let l=f[e];if(null==l)continue;let n=_(c[e]),i=S(l);1==t?M(O,n,C):M(O,D,i),M(O,n,i),C=i,D=n}let z=D;o&&1==t&&M(O,z=y+k,C);let[A,R]=t5(e,r);if(null!=u.fill||0!=A){let t=E.fill=new Path2D(O),l=S(u.fillTo(e,r,u.min,u.max,A));M(t,z,l),M(t,T,l)}if(!u.spanGaps){let o=[];o.push(...t6(c,f,a,s,P,_,i));let h=u.width*n/2,p=l||1==t?h:-h,m=l||-1==t?-h:h;o.forEach(e=>{e[0]+=p,e[1]+=m}),E.gaps=o=u.gaps(e,r,a,s,o),E.clip=t8(o,d.ori,g,b,v,x)}return 0!=R&&(E.band=2==R?[t7(e,r,a,s,O,-1),t7(e,r,a,s,O,1)]:t7(e,r,a,s,O,R)),E})},e.bars=function(e){let t=Q((e=e||eH).size,[.6,eb,1]),l=e.align||0,i=e.gap||0,o=e.radius,r=ey(o=null==o?[0,0]:"number"==typeof o?[o,0]:o),a=1-t[0],s=Q(t[1],eb),u=Q(t[2],1),c=Q(e.disp,eH),f=Q(e.each,e=>{}),{fill:d,stroke:h}=c;return(e,t,o,p)=>t3(e,t,(m,g,b,v,x,w,y,k,_,S,M)=>{let E,O,P=m.pxRound,C=l,j=i*n,D=s*n,T=u*n;0==v.ori?[E,O]=r(e,t):[O,E]=r(e,t);let z=v.dir*(0==v.ori?1:-1),A=0==v.ori?lo:lr,R=0==v.ori?f:(e,t,l,n,i,o,r)=>{f(e,t,l,i,n,r,o)},H=Q(e.bands,eN).find(e=>e.series[0]==t),N=null!=H?H.dir:0,Z=m.fillTo(e,t,m.min,m.max,N),I=P(y(Z,x,M,_)),W,Y,L,B=S,G=P(m.width*n),F=!1,V=null,U=null,q=null,X=null;null!=d&&(0==G||null!=h)&&(F=!0,V=d.values(e,t,o,p),U=new Map,new Set(V).forEach(e=>{null!=e&&U.set(e,new Path2D)}),G>0&&(q=h.values(e,t,o,p),X=new Map,new Set(q).forEach(e=>{null!=e&&X.set(e,new Path2D)})));let{x0:$,size:K}=c;if(null!=$&&null!=K){C=1,g=$.values(e,t,o,p),2==$.unit&&(g=g.map(t=>e.posToVal(k+t*S,v.key,!0)));let l=K.values(e,t,o,p);Y=2==K.unit?l[0]*S:w(l[0],v,S,k)-w(0,v,S,k),L=(B=lg(g,b,w,v,S,k,B))-Y+j}else L=(B=lg(g,b,w,v,S,k,B))*a+j,Y=B-L;L<1&&(L=0),G>=Y/2&&(G=0),L<5&&(P=ek);let J=L>0;Y=P(ex(B-L-(J?G:0),T,D)),W=(0==C?Y/2:C==z?0:Y)-C*z*((0==C?j/2:0)+(J?G/2:0));let ee={stroke:null,fill:null,clip:null,band:null,gaps:null,flags:0},et=F?null:new Path2D,el=null;if(null!=H)el=e.data[H.series[1]];else{let{y0:l,y1:n}=c;null!=l&&null!=n&&(b=n.values(e,t,o,p),el=l.values(e,t,o,p))}let en=E*Y,ei=O*Y;for(let l=1==z?o:p;l>=o&&l<=p;l+=z){let n=b[l];if(null==n)continue;if(null!=el){let e=el[l]??0;if(n-e==0)continue;I=y(e,x,M,_)}let i=w(2!=v.distr||null!=c?g[l]:l,v,S,k),o=y(Q(n,Z),x,M,_),r=P(i-W),a=P(ec(o,I)),s=P(eu(o,I)),u=a-s;if(null!=n){let i=n<0?ei:en,o=n<0?en:ei;F?(G>0&&null!=q[l]&&A(X.get(q[l]),r,s+er(G/2),Y,ec(0,u-G),i,o),null!=V[l]&&A(U.get(V[l]),r,s+er(G/2),Y,ec(0,u-G),i,o)):A(et,r,s+er(G/2),Y,ec(0,u-G),i,o),R(e,t,l,r-G/2,s,Y+G,u)}}return G>0?ee.stroke=F?X:et:F||(ee._fill=0==m.width?m._fill:m._stroke??m._fill,ee.width=0),ee.fill=F?U:et,ee})},e.spline=function(e){return function(e,t){let l=Q(t?.alignGaps,0);return(t,n,i,o)=>t3(t,n,(r,a,s,u,c,f,d,h,p,m,g)=>{let b,v,x;[i,o]=F(s,i,o);let w=r.pxRound,y=e=>w(f(e,u,m,h)),k=e=>w(d(e,c,g,p));0==u.ori?(b=lt,x=ln,v=lu):(b=ll,x=li,v=lc);let _=u.dir*(0==u.ori?1:-1),S=y(a[1==_?i:o]),M=S,E=[],O=[];for(let e=1==_?i:o;e>=i&&e<=o;e+=_)if(null!=s[e]){let t=y(a[e]);E.push(M=t),O.push(k(s[e]))}let P={stroke:e(E,O,b,x,v,w),fill:null,clip:null,band:null,gaps:null,flags:1},C=P.stroke,[j,D]=t5(t,n);if(null!=r.fill||0!=j){let e=P.fill=new Path2D(C),l=k(r.fillTo(t,n,r.min,r.max,j));x(e,M,l),x(e,S,l)}if(!r.spanGaps){let e=[];e.push(...t6(a,s,i,o,_,y,l)),P.gaps=e=r.gaps(t,n,i,o,e),P.clip=t8(e,u.ori,h,p,m,g)}return 0!=D&&(P.band=2==D?[t7(t,n,i,o,C,-1),t7(t,n,i,o,C,1)]:t7(t,n,i,o,C,D)),P})}(lb,e)}}}}]);