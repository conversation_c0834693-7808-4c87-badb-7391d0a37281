exports.id=540,exports.ids=[540],exports.modules={27053:(e,t,n)=>{"use strict";n.d(t,{Z:()=>c});var i=n(20997),s=n(16689),r=n.n(s),a=n(25537),o=n(47059),l=n.n(o);n(73198);let c=r().forwardRef((e,t)=>{let[n,r]=(0,s.useState)(""),[o,c]=(0,s.useState)(!1),E=(0,s.useRef)(null),d=(0,s.useRef)(null),u=(0,s.useRef)(null),[h,m]=(0,s.useState)("default");(0,s.useEffect)(()=>{t&&(t.current=d.current)},[t]),(0,s.useEffect)(()=>{void 0!==e.value&&null!==e.value&&String(e.value)!==n&&r(String(e.value))},[e.value]);let _=t=>{r(t),e.onChange&&e.onChange({target:{value:t,name:e.name}})};return(0,s.useEffect)(()=>{let e=e=>{!d.current||d.current.input.contains(e.target)||u.current?.contains(e.target)||c(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),(0,i.jsxs)(i.Fragment,{children:[i.jsx(a.Z,{...e,ref:d,value:n,onChange:t=>{r(t.target.value),e.onChange&&e.onChange(t)},onFocus:()=>{c(!0),setTimeout(()=>{E.current?.setInput(n)},100)},placeholder:e.placeholder??"",size:e.size??"middle",type:e.type}),o&&(0,i.jsxs)("div",{ref:u,className:"rsk-container",style:{position:"fixed",bottom:0,left:0,width:"100%",background:"#fff",boxShadow:"0 -2px 8px rgba(0,0,0,0.15)",zIndex:1e3},children:[i.jsx("div",{style:{padding:"8px 16px",borderBottom:"1px solid #e8e8e8",fontSize:"16px",fontWeight:"bold",background:"#fafafa",whiteSpace:"nowrap",overflowX:"auto"},children:"password"===e.type?"*".repeat(n.length):n||"\xa0"}),i.jsx(l(),{ref:E,onChange:_,onKeyPress:e=>{if("{shift}"===e||"{lock}"===e){m("default"===h?"shift":"default");return}if("{enter}"===e){c(!1),d.current?.blur();return}if("{bksp}"===e){_(n.slice(0,-1));return}if("{space}"===e){_(n+" ");return}"{enter}"===e&&(c(!1),d.current&&d.current.blur()),_(n+e)},layoutName:h,layout:{default:["1 2 3 4 5 6 7 8 9 0 - = {bksp}","q w e r t y u i o p [ ] \\","{lock} a s d f g h j k l ; ' {enter}","{shift} z x c v b n m , . / {shift}","{space}"],shift:["! @ # $ % ^ & * ( ) _ + {bksp}","Q W E R T Y U I O P { } |",'{lock} A S D F G H J K L : " {enter}',"{shift} Z X C V B N M < > ? {shift}","{space}"]},...e.keyboardOptions})]})]})})},362:(e,t,n)=>{"use strict";n.a(e,async(e,i)=>{try{n.d(t,{Z:()=>O});var s=n(20997),r=n(66488),a=n(93494),o=n(61834),l=n(82079),c=n(34665),E=n(69772),d=n(86001),u=n(23626),h=n(75504),m=n(16689),_=n(95471),g=n(38579),T=n.n(g),p=n(1635),f=n.n(p),A=n(27053),R=n(48764),D=n(7026),y=n(94886),b=e([_,R,D,y]);[_,R,D,y]=b.then?(await b)():b;let C=({key:e,data:t,setNotiData:n,setSearchedNotiData:i,setNotiDataWillBeShown:a})=>{let o=t?.severity==="notification",{client:l}=(0,R.T)();async function c(e){l.publish((0,D.te)(),JSON.stringify({...e,is_read:!0,customer_user:(0,y.WN)()}),e=>{e&&console.log("err: ",e)})}let d=async e=>{try{await (0,_.Qn)({name:e.name,is_read:!0}).then(t=>{if(!1===t.statusOK){E.ZP.error("C\xf3 lỗi xảy ra khi đang cập nhật th\xf4ng b\xe1o");return}c({name:e.name,message:e.message,created_at:e.created_at,entity:e.entity,type:"task",is_read:!0}),n(e=>e.map(e=>e.name===t?.responseData?.result?.data?.name?{...e,is_read:!0}:e)),i(e=>e.map(e=>e.name===t?.responseData?.result?.data?.name?{...e,is_read:!0}:e)),a(e=>e.map(e=>e.name===t?.responseData?.result?.data?.name?{...e,is_read:!0}:e))})}catch{E.ZP.error("C\xf3 lỗi xảy ra khi gửi lệnh cập nhật th\xf4ng b\xe1o")}};return(0,s.jsxs)("div",{className:"hoverable",style:{zIndex:1e3,cursor:"pointer",display:"flex",flexDirection:"row",alignItems:"center",gap:16,paddingTop:8,paddingBottom:8,color:"rgb(40,40,40)"},onClick:()=>d(t),children:[s.jsx(T(),{src:o?"/images/bell.png":"/images/calendar.png",alt:"icon",width:28,height:28,style:{filter:t.is_read?"grayscale(100%)":"none"}}),(0,s.jsxs)("div",{className:"flex-1 gap-2",children:[s.jsx("p",{style:{fontWeight:"bold",margin:0,color:t.is_read?"rgb(160,160,160)":"rgb(217, 161, 50)"},children:t.message}),(0,s.jsxs)("div",{style:{display:"flex",flexDirection:"row",alignItems:"center",gap:8,color:t.is_read?"rgb(160,160,160)":"rgb(217, 161, 50)"},children:[s.jsx(r.Z,{}),s.jsx("p",{style:{margin:0},children:f()(t.created_at).diff(f()(),"day")>0?`${f()(t.created_at).diff(f()(),"day")} ng\xe0y trước`:`${f()(t.created_at).format("DD/MM/YYYY HH:mm")}`})]})]})]},e)};function O(){let{client:e}=(0,R.T)(),[t,n]=(0,m.useState)([]),[i,r]=(0,m.useState)([]),[E,_]=(0,m.useState)([]),[g,T]=(0,m.useState)("all"),p=[{label:s.jsx("p",{children:"Tất cả"}),value:"all"},{label:(0,s.jsxs)("p",{children:[s.jsx(a.Z,{}),"\xa0Chưa xem"]}),value:"unseen"},{label:(0,s.jsxs)("p",{children:[s.jsx(o.Z,{}),"\xa0Đ\xe3 xem"]}),value:"seen"}],f=e=>{if(!e){r(t);return}let n=t.filter(t=>t.message.toLowerCase().includes(e));r(n)},D=(0,s.jsxs)("div",{style:{width:400,height:450,display:"flex",flexDirection:"column",gap:8},children:[s.jsx(A.Z,{suffix:s.jsx(l.Z,{}),placeholder:"T\xecm kiếm th\xf4ng b\xe1o",onChange:e=>f(e.target.value)}),s.jsx(d.ZP.Group,{block:!0,options:p,value:g,onChange:e=>T(e.target.value),optionType:"button",buttonStyle:"solid"}),s.jsx("div",{style:{width:"100%",height:400,overflowY:"scroll",display:"flex",flexDirection:"column",gap:8},children:E?.map(e=>s.jsx(C,{data:e,setNotiData:n,setSearchedNotiData:r,setNotiDataWillBeShown:_},e.name))})]});return s.jsx("div",{children:s.jsx(u.Z,{content:D,title:"Th\xf4ng b\xe1o",trigger:"click",children:s.jsx(h.ZP,{icon:s.jsx(c.Z,{style:{padding:4,color:"#45c3a1",fontSize:16}})})})})}i()}catch(e){i(e)}})},3159:(e,t,n)=>{"use strict";n.a(e,async(e,i)=>{try{n.d(t,{y:()=>a});var s=n(64194),r=e([s]);s=(r.then?(await r)():r)[0];class a{constructor(){this.handleReceiveMessage=(e,t)=>{try{let n=e.split("/")[e.split("/").length-2],i=JSON.parse(t.toString());if(Array.isArray(i)&&n){let e=i.filter(e=>"deviceId"!==e.key),t=e.map(e=>({...e,ts:parseInt(e.ts.toString())})),s=Date.now();e=e.filter(e=>e.ts>=s),this.emit({data:t,deviceIdThingsBoard:n})}}catch(e){}},this.events=new s.default,this.keyEvent="your_key"}emit(e){this.events.emit(this.keyEvent,e)}on(e){return this.events.on(this.keyEvent,e),{removeSubscribe:()=>{this.events.removeListener(this.keyEvent,e)}}}removeAllListeners(){this.events.removeAllListeners("message")}}i()}catch(e){i(e)}})},39939:(e,t,n)=>{"use strict";n.a(e,async(e,i)=>{try{n.d(t,{y:()=>o});var s=n(7026),r=n(64194),a=e([s,r]);[s,r]=a.then?(await a)():a;class o{constructor(){this.handleReceiveMessage=(e,t)=>{try{[(0,s.qk)(),(0,s.te)(),(0,s.cF)()].includes(e)&&t&&this.emit(JSON.parse(t.toString()))}catch(e){}},this.events=new r.default,this.keyEvent="message"}emit(e){this.events.emit(this.keyEvent,e)}on(e){return this.events.on(this.keyEvent,e),{removeSubscribe:()=>{this.events.removeListener(this.keyEvent,e)}}}removeAllListeners(){this.events.removeAllListeners("message")}}i()}catch(e){i(e)}})},71749:(e,t,n)=>{"use strict";n.a(e,async(e,i)=>{try{n.d(t,{C:()=>o});var s=n(7026),r=n(64194),a=e([s,r]);[s,r]=a.then?(await a)():a;class o{constructor(){this.handleReceiveMessage=(e,t)=>{try{e===(0,s.JK)()&&this.emit(JSON.parse(t.toString()))}catch(e){}},this.events=new r.default,this.keyEvent="notice:read-all"}emit(e){this.events.emit(this.keyEvent,e)}on(e){return this.events.on(this.keyEvent,e),{removeSubscribe:()=>{this.events.removeListener(this.keyEvent,e)}}}removeAllListeners(){this.events.removeAllListeners("message")}}i()}catch(e){i(e)}})},37164:(e,t,n)=>{"use strict";n.a(e,async(e,i)=>{try{n.d(t,{Z:()=>w});var s=n(20997),r=n(43840),a=n(75544),o=n(97318),l=n(29333),c=n(83825),E=n(34665),d=n(74306),u=n(63838),h=n(23626),m=n(75504),_=n(69772),g=n(68834),T=n(79894),p=n.n(T),f=n(16689),A=n(94744),R=n(68798),D=n(6013),y=n(185),b=n(60200),O=n(3671),C=n(85830),v=n(7965),I=n(48764),x=n(362),N=n(7026),P=e([A,D,b,C,I,x,N]);[A,D,b,C,I,x,N]=P.then?(await P)():P;let{Header:L,Content:S}=d.default,w=({children:e})=>{let t=[{key:"1",icon:s.jsx(r.Z,{}),label:s.jsx(p(),{href:"/vietplants/home",children:"Gi\xe1m s\xe1t"})},{key:"2",icon:s.jsx(a.Z,{}),label:s.jsx(p(),{href:"/vietplants/control",children:"Điều khiển"})},{key:"3",icon:s.jsx(o.Z,{}),label:s.jsx(p(),{href:"/vietplants/schedule_plan",children:"Lịch pha"})},{key:"4",icon:s.jsx(l.Z,{}),label:s.jsx(p(),{href:"/vietplants/setting",children:"C\xe0i đặt"})},{key:"5",icon:s.jsx(c.Z,{}),label:s.jsx(p(),{href:"/vietplants/program_execution_history",children:"Lịch sử"})},{key:"6",icon:s.jsx(E.Z,{}),label:s.jsx(p(),{href:"/vietplants/calibsensors",children:"Hiệu chuẩn"})}],n=(0,D.Z)(e=>e.languageData),{email:i}=(0,A.Z)(),{deviceData:T,setDeviceData:P,deviceId:w,setDeviceId:j,setIsOnline:k,functionListForControl:U,setFunctionListForControl:Y,setFunctionListForMonitor:M,functionListForCalibration:Z,setFunctionListForCalibration:G,calibrationInformation:V,setCalibrationInformation:F}=(0,b.Z)();(0,f.useEffect)(()=>{console.log("functionListForCalibration: ",Z)},[Z]),(0,f.useEffect)(()=>{!i||T&&Object.keys(T).length>0||(async()=>{try{let e=(await (0,y.pL)({})).find(e=>"b0b3cd50-3c27-11f0-98dc-bf024c096c4a"===e.device_id_thingsboard)||void 0;if(!e)throw Error("No device found");let t=!!e.latest_data.find(e=>"online"===e.key&&!0===e.value);P(e),j(e.device_id_thingsboard),k(t)}catch(e){console.error("Error fetching device list:",e)}})()},[i]);let J=(0,O.P)({deviceId:w,initOnline:!!T&&"latest_data"in T&&!!T.latest_data?.find(e=>"online"===e.key&&!0===e.value)});(0,f.useEffect)(()=>{w&&k(J.isOnline)},[J.isOnline,w,k]);let[$,H]=(0,f.useState)([]);(0,f.useEffect)(()=>{T&&0!==Object.keys(T).length&&"function_list"in T&&H(T.function_list)},[T]);let[W,K]=(0,f.useState)(!1);(0,f.useEffect)(()=>{if(0===$.length)return;let e=[],t=[],i=$.reduce((i,s)=>{if("Tab Break"===s.data_type)i.push({label:s.label,identifier:s.identifier,children:[]}),e.push({label:s.label,children:[]}),t.push({label:s.label,identifier:s.identifier,children:[]});else if("Group Break"===s.data_type){let n=i[i.length-1],r=e[e.length-1],a=t[t.length-1];n&&n.children.push({label:s.label,children:[]}),r&&r.children.push({label:s.label,children:[]}),a&&a.children.push({label:s.label,children:[]})}else{let r=i[i.length-1],a=e[e.length-1],o=t[t.length-1];r&&0===r.children.length&&r.children.push({label:n["common.control.tab.config.config"],children:[]}),a&&0===a.children.length&&a.children.push({label:n["common.control.tab.config.config"],children:[]}),o&&0===o.children.length&&o.children.push({label:n["common.control.tab.config.config"],children:[]});let l=r?.children[r.children.length-1],c=a?.children[a.children.length-1],E=o?.children[o.children.length-1];"rw"===s.data_permission?l?.children?.push(s):"r"===s.data_permission&&c?.children?.push(s),E?.children?.push(s)}return i},[]);Y(i.filter(e=>"pump_calibration"!==e.identifier)),M(e),G(t.filter(e=>"pump_calibration"===e.identifier)),K(!0)},[$]),(0,f.useEffect)(()=>{if(!Z||0===Z.length)return;let e=Array.from(Z?.[0]?.children?.[0]?.children).map(()=>({fulfill:!1,calibration:!1,startTimestampCalibration:null,totalTimeCalibration:0,isPumpActivedBefore:!1}));F(e)},[Z]);let{setScheduleProgramTriggerImmediately:B}=(0,C.Z)();(0,f.useEffect)(()=>{if(!W)return;let e=U.find(e=>"environment"===e.identifier);if(!e||0===e.children.length)return;let t=e.children[0]?.children?.[0];t&&B(t)},[W]);let{subscribe:z,unsubscribe:X}=(0,I.T)(),q=(0,f.useRef)([]);(0,f.useEffect)(()=>{if(w)return console.log("reload subscribe for app topic"),q.current=z([(0,v.v)(w),(0,N.te)()],e=>{console.log("message from broker for app topic:",e)}),()=>{X(q.current)}},[w]);let Q=()=>{window.ipc.send("close-app",null)},[ee,et]=(0,f.useState)(!1);return console.log("functionListForCalibration: ",Z),console.log("calibrationInformation: ",V),(0,s.jsxs)(d.default,{children:[(0,s.jsxs)(L,{style:{position:"sticky",top:0,zIndex:101,width:"100%",display:"flex",alignItems:"center",justifyContent:"space-between",backgroundColor:"rgba(250,250,250,0.5)",borderBottom:"solid #ddd 1px",backdropFilter:"blur(5px)",padding:0,height:"40px"},children:[(0,s.jsxs)("div",{style:{display:"flex",flexDirection:"row",alignItems:"center",height:"60px"},children:[s.jsx("div",{style:{display:"flex",alignItems:"center"},children:s.jsx("img",{src:"/images/Vietplants-logo.png",alt:"Logo",style:{height:30,marginRight:16,marginLeft:16}})}),s.jsx(u.Z,{mode:"horizontal",theme:"light",items:t,style:{height:"30px",display:"flex",alignItems:"center",justifyContent:"center",borderBottom:"none",backgroundColor:"rgba(250,250,250,0.1)",gap:0,overflowX:"scroll",minWidth:"700px"}})]}),(0,s.jsxs)("div",{style:{display:"flex",flexDirection:"row",alignItems:"center",gap:16},children:[s.jsx(x.Z,{}),s.jsx(h.Z,{content:(0,s.jsxs)("div",{style:{display:"flex",flexDirection:"row",gap:16},children:[s.jsx(m.ZP,{type:"primary",style:{marginRight:16},danger:!0,onClick:()=>{(0,R.rB)(),window.location.href="/user/login",_.ZP.success(n["common.logout.success"])},children:"Logout"}),s.jsx(m.ZP,{style:{borderColor:"orange"},onClick:()=>{window.ipc.send("minimize-app",null)},children:s.jsx("p",{style:{color:"orange"},children:"_"})}),s.jsx(m.ZP,{danger:!0,onClick:()=>et(!0),children:"X"}),s.jsx(g.Z,{open:ee,onCancel:()=>et(!1),onOk:()=>Q(),title:"Bạn muốn tắt ứng dụng ?"})]}),children:s.jsx("p",{style:{color:"black",marginRight:"20px",maxWidth:"150px",overflow:"scroll",height:"50px",fontSize:12},children:i})})]})]}),s.jsx(S,{style:{minHeight:"calc(100vh - 40px)",backgroundColor:"#f0f4f7"},children:e})]})};i()}catch(e){i(e)}})},39660:(e,t,n)=>{"use strict";n.d(t,{h:()=>i});let i={"common.login.success":"Login successful!","common.login.error":"Login failed!","common.logout.success":"Logout successful!","common.calibsensors.select_input_type":"Select input type","common.calibsensors.input.card":"Input","common.calibsensors.input.from_user":"From user","common.calibsensors.input.from_device":"From device","common.calibsensors.visualization.card":"Visualization","common.calibsensors.table.card":"Table","common.control.switch.on":"ON","common.control.switch.off":"OFF","common.control.post.success":"Control successful!","common.control.post.error":"Control failed!","common.control.tab.config.config":"Config"}},25088:(e,t,n)=>{"use strict";n.d(t,{L:()=>i});let i={"common.login.success":"Đăng nhập th\xe0nh c\xf4ng","common.login.error":"Đăng nhập thất bại","common.logout.success":"Đăng xuất th\xe0nh c\xf4ng","common.calibsensors.select_input_type":"Chọn loại ","common.calibsensors.input.card":"Số liệu ghi nhận","common.calibsensors.input.from_user":"Từ người d\xf9ng","common.calibsensors.input.from_device":"Từ thiết bị","common.calibsensors.visualization.card":"Biểu đồ","common.calibsensors.table.card":"Lịch sử ghi nhận","common.control.switch.on":"Bật","common.control.switch.off":"Tắt","common.control.post.success":"Điều khiển th\xe0nh c\xf4ng","common.control.post.error":"Điều khiển thất bại","common.control.tab.config.config":"Cấu h\xecnh"}},49540:(e,t,n)=>{"use strict";n.a(e,async(e,i)=>{try{n.r(t),n.d(t,{default:()=>g});var s=n(20997);n(72077),n(3581),n(81296);var r=n(16689),a=n.n(r),o=n(40968),l=n.n(o),c=n(37164),E=n(86677),d=n(48764),u=n(94744),h=n(75504),m=n(68834),_=e([c,d,u]);[c,d,u]=_.then?(await _)():_;let g=function({Component:e,pageProps:t}){(0,E.useRouter)(),(0,u.Z)(e=>e.email);let[n,i]=(0,r.useState)(!0),[o,_]=(0,r.useState)(!1),{connect:g}=(0,d.T)(),T=()=>{window.ipc.send("close-app",null)},[p,f]=(0,r.useState)(!1);return n?s.jsx("div",{children:"..."}):(0,s.jsxs)(a().Fragment,{children:[s.jsx(l(),{children:s.jsx("meta",{name:"viewport",content:"width=device-width, initial-scale=1"})}),o?s.jsx(c.Z,{children:s.jsx(e,{...t})}):(0,s.jsxs)(s.Fragment,{children:[s.jsx(e,{...t}),s.jsx(h.ZP,{style:{position:"fixed",top:"10px",left:"10px",borderColor:"orange"},onClick:()=>{window.ipc.send("minimize-app",null)},children:s.jsx("p",{style:{color:"orange"},children:"_"})}),s.jsx(h.ZP,{danger:!0,style:{position:"fixed",top:"10px",left:"60px"},onClick:()=>f(!0),children:"X"}),s.jsx(m.Z,{open:p,onCancel:()=>f(!1),onOk:()=>T(),title:"Bạn muốn tắt ứng dụng ?"})]})]})};i()}catch(e){i(e)}})},68798:(e,t,n)=>{"use strict";n.d(t,{rB:()=>o,v8:()=>r,x4:()=>a});var i=n(21158),s=n(345);async function r(){return(0,i.W)((0,s.rH)("api/v2/customerUser/user"),{method:"GET",headers:{"Content-Type":"application/json"}})}async function a(e,t){return(0,i.W)((0,s.rH)("api/v2/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},data:e,includeAuth:!1,...t||{}})}let o=async()=>(0,i.W)((0,s.rH)("api/auth/logout"),{method:"GET",includeAuth:!1})},185:(e,t,n)=>{"use strict";n.d(t,{$U:()=>o,A2:()=>a,gF:()=>l,pL:()=>r});var i=n(21158),s=n(345);async function r({page:e=1,size:t=20,fields:n=["*"],filters:r=[],or_filters:a=[],order_by:o="",group_by:l="",project_id:c=""}){try{let o={page:e,size:t,fields:JSON.stringify(n),filters:JSON.stringify(r),or_filters:JSON.stringify(a),projectId:c};return new URLSearchParams(o).toString(),(await (0,i.W)((0,s.rH)("api/v2/device/all-in-project/"),{method:"GET",params:o,queryParams:o})).responseData.result.map(e=>({...e,function_list:e.function_list.map(e=>({...e,data_eligible_max:e.data_eligible_max&&"string"==typeof e.data_eligible_max?parseInt(e.data_eligible_max):e.data_eligible_max,data_eligible_min:e.data_eligible_min&&"string"==typeof e.data_eligible_min?parseInt(e.data_eligible_min):e.data_eligible_min,data_measure_min:e.data_measure_min&&"string"==typeof e.data_measure_min?parseInt(e.data_measure_min):e.data_measure_min,data_measure_max:e.data_measure_max&&"string"==typeof e.data_measure_max?parseInt(e.data_measure_max):e.data_measure_max}))}))}catch(e){throw console.log(e),e}}async function a({device_id_thingsboard:e,method:t="set_state",params:n={}}){try{return(await (0,i.W)((0,s.rH)(`api/v2/thingsboard/rpc/oneway/${e}`),{method:"POST",data:{method:t,params:n,timeout:1e4}})).responseData.result}catch(e){throw e}}let o=async({deviceId:e,keys:t})=>(console.log("Getting latest data of deviceId: ",e),console.log("with keys: ",t),(await (0,i.W)((0,s.rH)(`api/v2/thingsboard/device-timeseries-latest/${e}?keys=${t.join(",")}`),{method:"GET"})).responseData.result);async function l({keys:e,startTs:t,endTs:n,agg:r="NONE",device_id_thingsboard:a,limit:o=100,interval:l}){try{return console.log("Getting data time series of deviceId: ",a),console.log("with keys: ",e),console.log("from: ",new Date(t)),console.log("to: ",new Date(n)),console.log("agg: ",r),console.log("limit: ",o),console.log("interval: ",l),(await (0,i.W)((0,s.rH)(`api/v2/thingsboard/get-device-timeseries-history/${a}?keys=${e}&startTs=${t}&endTs=${n}&agg=${r}&limit=${o}${l?"&interval="+l:""}`),{method:"GET"})).responseData.result}catch(e){throw e}}},3671:(e,t,n)=>{"use strict";n.d(t,{P:()=>s});var i=n(16689);let s=e=>{let[t,n]=(0,i.useState)(!!e.initOnline);return(0,i.useEffect)(()=>{n(!!e.initOnline)},[e.initOnline]),(0,i.useMemo)(()=>({isOnline:!!e.deviceId&&t}),[t,e.deviceId])}},95471:(e,t,n)=>{"use strict";n.a(e,async(e,i)=>{try{n.d(t,{Qn:()=>E});var s,r=n(22880),a=n(94886),o=n(21158),l=n(345),c=e([r,a]);async function E(e){return await (0,o.W)((0,l.rH)("api/v2/web-notification"),{method:"PUT",data:e})}[r,a]=c.then?(await c)():c,function(e){e.TECHNICIAN_EMPLOYEE="TECHNICIAN_EMPLOYEE",e.CUSTOMER_ADMIN="CUSTOMER_ADMIN",e.ADMIN_WAREHOUSE="ADMIN_WAREHOUSE"}(s||(s={})),i()}catch(e){i(e)}})},21158:(e,t,n)=>{"use strict";n.d(t,{Q:()=>r,W:()=>s});var i=n(16689);async function s(e,t){let n=t.headers?Object.fromEntries(Object.entries(t.headers).map(([e,t])=>[e,encodeURIComponent(t)])):{},i=e;if(t.params&&Object.keys(t.params).length){let n=new URLSearchParams;for(let[e,i]of Object.entries(t.params))"filters"===e&&Array.isArray(i)?n.set(e,JSON.stringify(i)):n.set(e,String(i));i=`${e}?${n.toString()}`}let{data:s,...r}=t,a=t?.includeAuth??!0,o=await fetch(i,{...r,headers:{...n,...a&&{Authorization:`Bearer ${encodeURIComponent(JSON.parse(localStorage.getItem("token")||"")?.token||"")}`},"Content-Type":"application/json"},body:s?JSON.stringify(s):void 0}),l=(o.headers.get("Content-Type")||"").includes("application/json")?await o.json():await o.text();return{statusOK:o.ok,responseData:l}}function r(e,t){let{onSuccess:n,onError:s}=t||{},[r,a]=(0,i.useState)(!1),[o,l]=(0,i.useState)(null),[c,E]=(0,i.useState)(null);return{loading:r,data:o,error:c,run:(0,i.useCallback)(async(...t)=>{a(!0),E(null);try{let i=await e(...t);return l(i),n?.(i,t),i}catch(e){return E(e),s?.(e,t),Promise.reject(e)}finally{a(!1)}},[e,n,s])}}},345:(e,t,n)=>{"use strict";n.d(t,{rH:()=>i});let i=e=>`http://172.30.108.85:1881/${e}`},60200:(e,t,n)=>{"use strict";n.a(e,async(e,i)=>{try{n.d(t,{Z:()=>u});var s=n(66912),r=n(53868),a=e([s,r]);[s,r]=a.then?(await a)():a;let o=new Date,l=new Date;l.setMonth(l.getMonth()-1);let c=l.getTime(),E=o.getTime(),d=[c,E],u=(0,s.create)((0,r.immer)((e,t)=>({deviceData:void 0,setDeviceData:t=>e({deviceData:t}),loadingDataDevice:!1,deviceId:"",setDeviceId:t=>e({deviceId:t}),isOnline:!1,setIsOnline:t=>e({isOnline:t}),functionListForMonitor:[],setFunctionListForMonitor:t=>e({functionListForMonitor:t}),setTimeRef:e=>{d=e},getTimeRange:()=>d,functionListForControl:[],setFunctionListForControl:t=>e({functionListForControl:t}),functionListForCalibration:[],setFunctionListForCalibration:t=>e({functionListForCalibration:t}),calibrationInformation:[],setCalibrationInformation:t=>e({calibrationInformation:t})})));i()}catch(e){i(e)}})},6013:(e,t,n)=>{"use strict";n.a(e,async(e,i)=>{try{n.d(t,{Z:()=>c});var s=n(66912),r=n(53868),a=n(25088),o=n(39660),l=e([s,r]);[s,r]=l.then?(await l)():l;let c=(0,s.create)((0,r.immer)((e,t)=>({language:"vi-VN",languageData:a.L,setLanguage:t=>{e({language:t}),e({languageData:"vi-VN"===t?a.L:o.h})}})));i()}catch(e){i(e)}})},48764:(e,t,n)=>{"use strict";n.a(e,async(e,i)=>{try{n.d(t,{T:()=>_});var s=n(28323),r=n.n(s),a=n(66912),o=n(53868),l=n(3159),c=n(39939),E=n(7026),d=n(94886),u=n(71749),h=e([a,o,l,c,E,d,u]);[a,o,l,c,E,d,u]=h.then?(await h)():h;let m=()=>{let e=localStorage.getItem("token"),t=e?JSON.parse(e):{};return{clean:!0,connectTimeout:4e3,clientId:Math.random().toString(16).slice(2),username:"unused",password:t?.accessToken||"",keepalive:300,reconnectPeriod:1e3}},_=(0,a.create)((0,o.immer)((e,t)=>({client:null,connected:!1,subscriptions:{},disconnect:()=>{let{client:n}=t();n&&(n.end(),e({client:null,subscriptions:{}}))},connect:n=>{if(console.log("brokerUrl: ",n),t().client)return;let i=r().connect(n,{...m()});i.on("connect",()=>{e({connected:!0})}),i.on("reconnect",()=>{console.log("Reconnecting to broker...")}),i.on("message",(e,n)=>{let i=t().subscriptions;i[e]&&i[e].forEach(e=>{try{e.callback(n.toString())}catch(e){console.log("error: ",e)}})}),i.on("offline",()=>{e({connected:!1}),console.log("Client is offline")}),i.on("error",e=>{console.error("Connection error:",e)}),i.on("disconnect",()=>{e({connected:!1}),console.log("Disconnected from broker")}),e({client:i})},subscribe:(n,i)=>{let{client:s,subscriptions:r}=t(),a=Math.random().toString(16).slice(2);return s?(n.forEach(t=>{r[t]?e(e=>{e.subscriptions[t].push({id:a,callback:i})}):s.subscribe(t,n=>{n||(e(e=>{e.subscriptions[t]||(e.subscriptions[t]=[]),e.subscriptions[t].push({id:a,callback:i})}),console.log(`Subscribed to ${t}`))})}),[a]):[]},unsubscribe:n=>{let{client:i,subscriptions:s}=t();i&&Object.keys(s).forEach(t=>{let r=s[t].filter(e=>!n.includes(e.id));0===r.length?i.unsubscribe(t,n=>{n||(e(e=>{delete e.subscriptions[t]}),console.log(`Unsubscribed from ${t}`))}):(e(e=>{e.subscriptions[t]=r}),console.log(`Unsubscribed from - event ${t}`))})},handleMessage:{deviceHandle:{receive:new l.y,emit:{}},noticeHandle:{receive:new c.y,emit:{updateNoticeTask:e=>{let{client:n}=t();n&&n.publish((0,E.te)(),JSON.stringify({...e,is_read:!0,customer_user:(0,d.g7)()}),e=>{e&&console.log("err: ",e)})}}},noticeHandleReadAll:{receive:new u.C,emit:{readAll:e=>{let{client:n}=t();n&&n.publish((0,E.JK)(),JSON.stringify(e),e=>{e&&console.log("err: ",e)})}}}}})));i()}catch(e){i(e)}})},7965:(e,t,n)=>{"use strict";n.d(t,{v:()=>i});let i=e=>`viis/things/v2/${e}/telemetry`},85830:(e,t,n)=>{"use strict";n.a(e,async(e,i)=>{try{n.d(t,{Z:()=>o});var s=n(66912),r=n(53868),a=e([s,r]);[s,r]=a.then?(await a)():a;let o=(0,s.create)((0,r.immer)((e,t)=>({schedulePlans:[],setSchedulePlans:t=>e({schedulePlans:t}),scheduleProgramTriggerImmediately:null,setScheduleProgramTriggerImmediately:t=>e({scheduleProgramTriggerImmediately:t})})));i()}catch(e){i(e)}})},94744:(e,t,n)=>{"use strict";n.a(e,async(e,i)=>{try{n.d(t,{Z:()=>o});var s=n(66912),r=n(53868),a=e([s,r]);[s,r]=a.then?(await a)():a;let o=(0,s.create)((0,r.immer)((e,t)=>({email:"",setEmail:t=>e({email:t})})));i()}catch(e){i(e)}})},59942:(e,t,n)=>{"use strict";var i,s;n.d(t,{g8:()=>i}),function(e){e.TECHNICIAN_EMPLOYEE="TECHNICIAN_EMPLOYEE",e.CUSTOMER_ADMIN="CUSTOMER_ADMIN",e.ADMIN_WAREHOUSE="ADMIN_WAREHOUSE"}(i||(i={})),function(e){e.SYSTEM_ADMIN="SYSTEM_ADMIN",e.CROP_CREATE="CROP_CREATE",e.CROP_READ="CROP_READ",e.CROP_UPDATE="CROP_UPDATE",e.CROP_DELETE="CROP_DELETE",e.TASK_CREATE="TASK_CREATE",e.TASK_READ="TASK_READ",e.TASK_UPDATE="TASK_UPDATE",e.TASK_DELETE="TASK_DELETE",e.PLAN_CREATE="PLAN_CREATE",e.PLAN_READ="PLAN_READ",e.PLAN_UPDATE="PLAN_UPDATE",e.PLAN_DELETE="PLAN_DELETE",e.STATE_CREATE="STATE_CREATE",e.STATE_READ="STATE_READ",e.STATE_UPDATE="STATE_UPDATE",e.STATE_DELETE="STATE_DELETE",e.PLANT_CREATE="PLANT_CREATE",e.PLANT_READ="PLANT_READ",e.PLANT_UPDATE="PLANT_UPDATE",e.PLANT_DELETE="PLANT_DELETE",e.PROJECT_CREATE="PROJECT_CREATE",e.PROJECT_READ="PROJECT_READ",e.PROJECT_UPDATE="PROJECT_UPDATE",e.PROJECT_DELETE="PROJECT_DELETE",e.ZONE_CREATE="ZONE_CREATE",e.ZONE_READ="ZONE_READ",e.ZONE_UPDATE="ZONE_UPDATE",e.ZONE_DELETE="ZONE_DELETE",e.CATEGORY_CREATE="CATEGORY_CREATE",e.CATEGORY_READ="CATEGORY_READ",e.CATEGORY_UPDATE="CATEGORY_UPDATE",e.CATEGORY_DELETE="CATEGORY_DELETE",e.PRODUCT_CREATE="PRODUCT_CREATE",e.PRODUCT_READ="PRODUCT_READ",e.PRODUCT_UPDATE="PRODUCT_UPDATE",e.PRODUCT_DELETE="PRODUCT_DELETE",e.STORAGE_CREATE="STORAGE_CREATE",e.STORAGE_READ="STORAGE_READ",e.STORAGE_UPDATE="STORAGE_UPDATE",e.STORAGE_DELETE="STORAGE_DELETE",e.CATEGORY_INVENTORY_CREATE="CATEGORY_INVENTORY_CREATE",e.CATEGORY_INVENTORY_READ="CATEGORY_INVENTORY_READ",e.CATEGORY_INVENTORY_UPDATE="CATEGORY_INVENTORY_UPDATE",e.CATEGORY_INVENTORY_DELETE="CATEGORY_INVENTORY_DELETE",e.CATEGORY_INVENTORY_FIELD_LEVEL_READ="CATEGORY_INVENTORY_FIELD_LEVEL_READ",e.CATEGORY_INVENTORY_FIELD_LEVEL_WRITE="CATEGORY_INVENTORY_FIELD_LEVEL_WRITE",e.PRODUCT_INVENTORY_CREATE="PRODUCT_INVENTORY_CREATE",e.PRODUCT_INVENTORY_READ="PRODUCT_INVENTORY_READ",e.PRODUCT_INVENTORY_UPDATE="PRODUCT_INVENTORY_UPDATE",e.PRODUCT_INVENTORY_DELETE="PRODUCT_INVENTORY_DELETE",e.EMPLOYEE_CREATE="EMPLOYEE_CREATE",e.EMPLOYEE_READ="EMPLOYEE_READ",e.EMPLOYEE_UPDATE="EMPLOYEE_UPDATE",e.EMPLOYEE_DELETE="EMPLOYEE_DELETE",e.DYNAMIC_ROLE_CREATE="DYNAMIC_ROLE_CREATE",e.DYNAMIC_ROLE_READ="DYNAMIC_ROLE_READ",e.DYNAMIC_ROLE_UPDATE="DYNAMIC_ROLE_UPDATE",e.DYNAMIC_ROLE_DELETE="DYNAMIC_ROLE_DELETE",e.TIMEKEEPING_CREATE="TIMEKEEPING_CREATE",e.TIMEKEEPING_READ="TIMEKEEPING_READ",e.TIMEKEEPING_UPDATE="TIMEKEEPING_UPDATE",e.TIMEKEEPING_DELETE="TIMEKEEPING_DELETE",e.VISITOR_CREATE="VISITOR_CREATE",e.VISITOR_READ="VISITOR_READ",e.VISITOR_UPDATE="VISITOR_UPDATE",e.VISITOR_DELETE="VISITOR_DELETE",e.IOT_DEVICE_CREATE="IOT_DEVICE_CREATE",e.IOT_DEVICE_READ="IOT_DEVICE_READ",e.IOT_DEVICE_UPDATE="IOT_DEVICE_UPDATE",e.IOT_DEVICE_DELETE="IOT_DEVICE_DELETE"}(s||(s={}))},94886:(e,t,n)=>{"use strict";n.a(e,async(e,i)=>{try{n.d(t,{WN:()=>E,g7:()=>c});var s=n(59942),r=n(22880),a=e([r]);r=(a.then?(await a)():a)[0];let o=()=>{try{return JSON.parse(localStorage.getItem("token")||"{}").token||null}catch{return null}},l=()=>{try{let e=o(),t=(0,r.jwtDecode)(e);return t.is_admin&&t.user_role.push(s.g8.CUSTOMER_ADMIN),t}catch(e){return null}},c=()=>{let e=l();return e?.user_id},E=()=>{let e=l();return e?.customer_id};i()}catch(e){i(e)}})},7026:(e,t,n)=>{"use strict";n.a(e,async(e,i)=>{try{n.d(t,{JK:()=>c,cF:()=>o,qk:()=>a,te:()=>l});var s=n(94886),r=e([s]);s=(r.then?(await r)():r)[0];let a=()=>{let e=(0,s.g7)();return`viis/web-notification/${e}`},o=()=>{let e=(0,s.WN)();return`viis/web-notification/${e}`},l=()=>{let e=(0,s.g7)();return`viis/web-notification/${e}`},c=()=>{let e=(0,s.g7)();return`viis/web-notification/${e}/read-all`};i()}catch(e){i(e)}})},81296:()=>{}};