{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:file((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/]+\\.\\w+)/", "destination": "/:file", "internal": true, "missing": [{"type": "header", "key": "x-nextjs-data"}], "statusCode": 308, "regex": "^(?:/((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/]+\\.\\w+))/$"}, {"source": "/:notfile((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/\\.]+)", "destination": "/:notfile/", "internal": true, "statusCode": 308, "regex": "^(?:/((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/\\.]+))$"}], "headers": [], "dynamicRoutes": [], "staticRoutes": [{"page": "/user/forgot-password", "regex": "^/user/forgot\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/user/forgot\\-password(?:/)?$"}, {"page": "/user/login", "regex": "^/user/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/user/login(?:/)?$"}, {"page": "/vietplants/authentication", "regex": "^/vietplants/authentication(?:/)?$", "routeKeys": {}, "namedRegex": "^/vietplants/authentication(?:/)?$"}, {"page": "/vietplants/calibsensors", "regex": "^/vietplants/calibsensors(?:/)?$", "routeKeys": {}, "namedRegex": "^/vietplants/calibsensors(?:/)?$"}, {"page": "/vietplants/control", "regex": "^/vietplants/control(?:/)?$", "routeKeys": {}, "namedRegex": "^/vietplants/control(?:/)?$"}, {"page": "/vietplants/home", "regex": "^/vietplants/home(?:/)?$", "routeKeys": {}, "namedRegex": "^/vietplants/home(?:/)?$"}, {"page": "/vietplants/program_execution_history", "regex": "^/vietplants/program_execution_history(?:/)?$", "routeKeys": {}, "namedRegex": "^/vietplants/program_execution_history(?:/)?$"}, {"page": "/vietplants/schedule", "regex": "^/vietplants/schedule(?:/)?$", "routeKeys": {}, "namedRegex": "^/vietplants/schedule(?:/)?$"}, {"page": "/vietplants/schedule_plan", "regex": "^/vietplants/schedule_plan(?:/)?$", "routeKeys": {}, "namedRegex": "^/vietplants/schedule_plan(?:/)?$"}, {"page": "/vietplants/schedule_program", "regex": "^/vietplants/schedule_program(?:/)?$", "routeKeys": {}, "namedRegex": "^/vietplants/schedule_program(?:/)?$"}, {"page": "/vietplants/setting", "regex": "^/vietplants/setting(?:/)?$", "routeKeys": {}, "namedRegex": "^/vietplants/setting(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}