"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[670],{4374:function(e,t,o){o.d(t,{Z:function(){return y}});var r=o(7378),n=o(5),l=o.n(n),c=o(5610);function a(e){return["small","middle","large"].includes(e)}function s(e){return!!e&&"number"==typeof e&&!Number.isNaN(e)}var i=o(8539),u=o(3761);let d=r.createContext({latestIndex:0}),p=d.Provider;var m=e=>{let{className:t,index:o,children:n,split:l,style:c}=e,{latestIndex:a}=r.useContext(d);return null==n?null:r.createElement(r.Fragment,null,r.createElement("div",{className:t,style:c},n),o<a&&l&&r.createElement("span",{className:"".concat(t,"-split")},l))},g=o(9212),b=function(e,t){var o={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(o[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,r=Object.getOwnPropertySymbols(e);n<r.length;n++)0>t.indexOf(r[n])&&Object.prototype.propertyIsEnumerable.call(e,r[n])&&(o[r[n]]=e[r[n]]);return o};let f=r.forwardRef((e,t)=>{var o;let{getPrefixCls:n,direction:u,size:d,className:f,style:y,classNames:v,styles:C}=(0,i.dj)("space"),{size:h=null!=d?d:"small",align:k,className:O,rootClassName:w,children:x,direction:E="horizontal",prefixCls:S,split:j,style:N,wrap:P=!1,classNames:I,styles:T}=e,B=b(e,["size","align","className","rootClassName","children","direction","prefixCls","split","style","wrap","classNames","styles"]),[z,Z]=Array.isArray(h)?h:[h,h],A=a(Z),H=a(z),M=s(Z),R=s(z),_=(0,c.Z)(x,{keepEmpty:!0}),F=void 0===k&&"horizontal"===E?"center":k,L=n("space",S),[q,W,D]=(0,g.Z)(L),G=l()(L,f,W,"".concat(L,"-").concat(E),{["".concat(L,"-rtl")]:"rtl"===u,["".concat(L,"-align-").concat(F)]:F,["".concat(L,"-gap-row-").concat(Z)]:A,["".concat(L,"-gap-col-").concat(z)]:H},O,w,D),X=l()("".concat(L,"-item"),null!==(o=null==I?void 0:I.item)&&void 0!==o?o:v.item),Q=0,U=_.map((e,t)=>{var o;null!=e&&(Q=t);let n=(null==e?void 0:e.key)||"".concat(X,"-").concat(t);return r.createElement(m,{className:X,key:n,index:t,split:j,style:null!==(o=null==T?void 0:T.item)&&void 0!==o?o:C.item},e)}),$=r.useMemo(()=>({latestIndex:Q}),[Q]);if(0===_.length)return null;let J={};return P&&(J.flexWrap="wrap"),!H&&R&&(J.columnGap=z),!A&&M&&(J.rowGap=Z),q(r.createElement("div",Object.assign({ref:t,className:G,style:Object.assign(Object.assign(Object.assign({},J),y),N)},B),r.createElement(p,{value:$},U)))});f.Compact=u.ZP;var y=f},1468:function(e,t,o){o.d(t,{Z:function(){return I}});var r=o(7378),n=o(5),l=o.n(n),c=o(8596),a=o(1399),s=o(7921),i=o(7248),u=o(7881),d=o(8539),p=o(7349),m=o(5874),g=o(5334),b=o(4645),f=o(4547);let y=e=>{let{paddingXXS:t,lineWidth:o,tagPaddingHorizontal:r,componentCls:n,calc:l}=e,c=l(r).sub(o).equal(),a=l(t).sub(o).equal();return{[n]:Object.assign(Object.assign({},(0,g.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:c,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:"".concat((0,p.bf)(e.lineWidth)," ").concat(e.lineType," ").concat(e.colorBorder),borderRadius:e.borderRadiusSM,opacity:1,transition:"all ".concat(e.motionDurationMid),textAlign:"start",position:"relative",["&".concat(n,"-rtl")]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},["".concat(n,"-close-icon")]:{marginInlineStart:a,fontSize:e.tagIconSize,color:e.colorTextDescription,cursor:"pointer",transition:"all ".concat(e.motionDurationMid),"&:hover":{color:e.colorTextHeading}},["&".concat(n,"-has-color")]:{borderColor:"transparent",["&, a, a:hover, ".concat(e.iconCls,"-close, ").concat(e.iconCls,"-close:hover")]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",["&:not(".concat(n,"-checkable-checked):hover")]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},["> ".concat(e.iconCls," + span, > span + ").concat(e.iconCls)]:{marginInlineStart:c}}),["".concat(n,"-borderless")]:{borderColor:"transparent",background:e.tagBorderlessBg}}},v=e=>{let{lineWidth:t,fontSizeIcon:o,calc:r}=e,n=e.fontSizeSM;return(0,b.IX)(e,{tagFontSize:n,tagLineHeight:(0,p.bf)(r(e.lineHeightSM).mul(n).equal()),tagIconSize:r(o).sub(r(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},C=e=>({defaultBg:new m.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var h=(0,f.I$)("Tag",e=>y(v(e)),C),k=function(e,t){var o={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(o[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,r=Object.getOwnPropertySymbols(e);n<r.length;n++)0>t.indexOf(r[n])&&Object.prototype.propertyIsEnumerable.call(e,r[n])&&(o[r[n]]=e[r[n]]);return o};let O=r.forwardRef((e,t)=>{let{prefixCls:o,style:n,className:c,checked:a,onChange:s,onClick:i}=e,u=k(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:p,tag:m}=r.useContext(d.E_),g=p("tag",o),[b,f,y]=h(g),v=l()(g,"".concat(g,"-checkable"),{["".concat(g,"-checkable-checked")]:a},null==m?void 0:m.className,c,f,y);return b(r.createElement("span",Object.assign({},u,{ref:t,style:Object.assign(Object.assign({},n),null==m?void 0:m.style),className:v,onClick:e=>{null==s||s(!a),null==i||i(e)}})))});var w=o(2019);let x=e=>(0,w.Z)(e,(t,o)=>{let{textColor:r,lightBorderColor:n,lightColor:l,darkColor:c}=o;return{["".concat(e.componentCls).concat(e.componentCls,"-").concat(t)]:{color:r,background:l,borderColor:n,"&-inverse":{color:e.colorTextLightSolid,background:c,borderColor:c},["&".concat(e.componentCls,"-borderless")]:{borderColor:"transparent"}}}});var E=(0,f.bk)(["Tag","preset"],e=>x(v(e)),C);let S=(e,t,o)=>{let r="string"!=typeof o?o:o.charAt(0).toUpperCase()+o.slice(1);return{["".concat(e.componentCls).concat(e.componentCls,"-").concat(t)]:{color:e["color".concat(o)],background:e["color".concat(r,"Bg")],borderColor:e["color".concat(r,"Border")],["&".concat(e.componentCls,"-borderless")]:{borderColor:"transparent"}}}};var j=(0,f.bk)(["Tag","status"],e=>{let t=v(e);return[S(t,"success","Success"),S(t,"processing","Info"),S(t,"error","Error"),S(t,"warning","Warning")]},C),N=function(e,t){var o={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(o[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,r=Object.getOwnPropertySymbols(e);n<r.length;n++)0>t.indexOf(r[n])&&Object.prototype.propertyIsEnumerable.call(e,r[n])&&(o[r[n]]=e[r[n]]);return o};let P=r.forwardRef((e,t)=>{let{prefixCls:o,className:n,rootClassName:p,style:m,children:g,icon:b,color:f,onClose:y,bordered:v=!0,visible:C}=e,k=N(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:O,direction:w,tag:x}=r.useContext(d.E_),[S,P]=r.useState(!0),I=(0,c.Z)(k,["closeIcon","closable"]);r.useEffect(()=>{void 0!==C&&P(C)},[C]);let T=(0,a.o2)(f),B=(0,a.yT)(f),z=T||B,Z=Object.assign(Object.assign({backgroundColor:f&&!z?f:void 0},null==x?void 0:x.style),m),A=O("tag",o),[H,M,R]=h(A),_=l()(A,null==x?void 0:x.className,{["".concat(A,"-").concat(f)]:z,["".concat(A,"-has-color")]:f&&!z,["".concat(A,"-hidden")]:!S,["".concat(A,"-rtl")]:"rtl"===w,["".concat(A,"-borderless")]:!v},n,p,M,R),F=e=>{e.stopPropagation(),null==y||y(e),e.defaultPrevented||P(!1)},[,L]=(0,s.Z)((0,s.w)(e),(0,s.w)(x),{closable:!1,closeIconRender:e=>{let t=r.createElement("span",{className:"".concat(A,"-close-icon"),onClick:F},e);return(0,i.wm)(e,t,e=>({onClick:t=>{var o;null===(o=null==e?void 0:e.onClick)||void 0===o||o.call(e,t),F(t)},className:l()(null==e?void 0:e.className,"".concat(A,"-close-icon"))}))}}),q="function"==typeof k.onClick||g&&"a"===g.type,W=b||null,D=W?r.createElement(r.Fragment,null,W,g&&r.createElement("span",null,g)):g,G=r.createElement("span",Object.assign({},I,{ref:t,className:_,style:Z}),D,L,T&&r.createElement(E,{key:"preset",prefixCls:A}),B&&r.createElement(j,{key:"status",prefixCls:A}));return H(q?r.createElement(u.Z,{component:"Tag"},G):G)});P.CheckableTag=O;var I=P},5178:function(e,t,o){o.d(t,{G:function(){return c}});var r=o(8506),n=function(e){if((0,r.Z)()&&window.document.documentElement){var t=Array.isArray(e)?e:[e],o=window.document.documentElement;return t.some(function(e){return e in o.style})}return!1},l=function(e,t){if(!n(e))return!1;var o=document.createElement("div"),r=o.style[e];return o.style[e]=t,o.style[e]!==r};function c(e,t){return Array.isArray(e)||void 0===t?n(e):l(e,t)}}}]);