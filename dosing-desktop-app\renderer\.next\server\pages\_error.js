"use strict";(()=>{var e={};e.id=820,e.ids=[820,888,660],e.modules={38847:(e,r)=>{Object.defineProperty(r,"l",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},2769:(e,r,t)=>{t.a(e,async(e,o)=>{try{t.r(r),t.d(r,{config:()=>h,default:()=>c,getServerSideProps:()=>g,getStaticPaths:()=>f,getStaticProps:()=>x,reportWebVitals:()=>m,routeModule:()=>_,unstable_getServerProps:()=>q,unstable_getServerSideProps:()=>y,unstable_getStaticParams:()=>S,unstable_getStaticPaths:()=>b,unstable_getStaticProps:()=>P});var i=t(39847),s=t(2603),n=t(38847),a=t(30218),l=t.n(a),u=t(49540),d=t(6615),p=e([u]);u=(p.then?(await p)():p)[0];let c=(0,n.l)(d,"default"),x=(0,n.l)(d,"getStaticProps"),f=(0,n.l)(d,"getStaticPaths"),g=(0,n.l)(d,"getServerSideProps"),h=(0,n.l)(d,"config"),m=(0,n.l)(d,"reportWebVitals"),P=(0,n.l)(d,"unstable_getStaticProps"),b=(0,n.l)(d,"unstable_getStaticPaths"),S=(0,n.l)(d,"unstable_getStaticParams"),q=(0,n.l)(d,"unstable_getServerProps"),y=(0,n.l)(d,"unstable_getServerSideProps"),_=new i.PagesRouteModule({definition:{kind:s.x.PAGES,page:"/_error",pathname:"/_error",bundlePath:"",filename:""},components:{App:u.default,Document:l()},userland:d});o()}catch(e){o(e)}})},6615:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"default",{enumerable:!0,get:function(){return d}});let o=t(81763),i=t(20997),s=o._(t(16689)),n=o._(t(89557)),a={400:"Bad Request",404:"This page could not be found",405:"Method Not Allowed",500:"Internal Server Error"};function l(e){let{res:r,err:t}=e;return{statusCode:r&&r.statusCode?r.statusCode:t?t.statusCode:404}}let u={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},desc:{lineHeight:"48px"},h1:{display:"inline-block",margin:"0 20px 0 0",paddingRight:23,fontSize:24,fontWeight:500,verticalAlign:"top"},h2:{fontSize:14,fontWeight:400,lineHeight:"28px"},wrap:{display:"inline-block"}};class d extends s.default.Component{render(){let{statusCode:e,withDarkMode:r=!0}=this.props,t=this.props.title||a[e]||"An unexpected error has occurred";return(0,i.jsxs)("div",{style:u.error,children:[(0,i.jsx)(n.default,{children:(0,i.jsx)("title",{children:e?e+": "+t:"Application error: a client-side exception has occurred"})}),(0,i.jsxs)("div",{style:u.desc,children:[(0,i.jsx)("style",{dangerouslySetInnerHTML:{__html:"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}"+(r?"@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}":"")}}),e?(0,i.jsx)("h1",{className:"next-error-h1",style:u.h1,children:e}):null,(0,i.jsx)("div",{style:u.wrap,children:(0,i.jsxs)("h2",{style:u.h2,children:[this.props.title||e?t:(0,i.jsx)(i.Fragment,{children:"Application error: a client-side exception has occurred (see the browser console for more information)"}),"."]})})]})]})}}d.displayName="ErrorPage",d.getInitialProps=l,d.origGetInitialProps=l,("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},2603:(e,r)=>{var t;Object.defineProperty(r,"x",{enumerable:!0,get:function(){return t}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(t||(t={}))},34053:e=>{e.exports=require("@ant-design/colors")},52727:e=>{e.exports=require("@ant-design/cssinjs")},10045:e=>{e.exports=require("@ant-design/cssinjs-utils")},79712:e=>{e.exports=require("@ant-design/fast-color")},98810:e=>{e.exports=require("@rc-component/color-picker")},59003:e=>{e.exports=require("classnames")},1635:e=>{e.exports=require("dayjs")},28323:e=>{e.exports=require("mqtt")},62785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},40968:e=>{e.exports=require("next/head")},92090:e=>{e.exports=require("rc-checkbox")},93426:e=>{e.exports=require("rc-dialog")},57118:e=>{e.exports=require("rc-field-form")},59934:e=>{e.exports=require("rc-menu")},94290:e=>{e.exports=require("rc-motion")},78858:e=>{e.exports=require("rc-notification")},18880:e=>{e.exports=require("rc-textarea")},93283:e=>{e.exports=require("rc-tooltip")},16689:e=>{e.exports=require("react")},66405:e=>{e.exports=require("react-dom")},1469:e=>{e.exports=require("react-is")},47059:e=>{e.exports=require("react-simple-keyboard")},20997:e=>{e.exports=require("react/jsx-runtime")},92048:e=>{e.exports=require("fs")},55315:e=>{e.exports=require("path")},76162:e=>{e.exports=require("stream")},71568:e=>{e.exports=require("zlib")},64194:e=>{e.exports=import("eventemitter3")},22880:e=>{e.exports=import("jwt-decode")},66912:e=>{e.exports=import("zustand")},53868:e=>{e.exports=import("zustand/middleware/immer")}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[417,753,218,540],()=>t(2769));module.exports=o})();