(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[943],{8026:function(e,l,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/user/forgot-password",function(){return t(587)}])},587:function(e,l,t){"use strict";t.r(l);var n=t(4246);t(7378);var r=t(274);l.default=()=>(0,n.jsx)(r.ForgotPasswordPage,{})},274:function(e,l,t){"use strict";t.r(l),t.d(l,{ForgotPasswordPage:function(){return y},LoginPage:function(){return m}});var n=t(4246),r=t(5475),i=t(3992),o=t(4478),s=t(8218),a=t(7740),d=t(6316),c=t(3779),u=t(2882),p=t(624),g=t(5942),h=t(7378),x=t(4717),f=t(1555);let m=()=>{let e=(0,x.Z)(e=>e.languageData),l=(0,g.Z)(e=>e.setEmail),[t,c]=(0,h.useState)([]);(0,h.useEffect)(()=>{!async function(){try{var e;let l=await (0,p.v8)();c(null==l?void 0:null===(e=l.responseData)||void 0===e?void 0:e.data)}catch(e){r.ZP.error("C\xf3 lỗi xảy ra khi lấy th\xf4ng tin người d\xf9ng! Vui l\xf2ng thử lại sau.")}}()},[]);let[m,y]=(0,h.useState)([]);(0,h.useEffect)(()=>{console.log("listOfAllValidUsers: ",t),t&&(null==t?void 0:t.length)!==0&&y(t.map(e=>({label:(0,n.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:4,borderBottom:"1px solid #ddd"},children:[(0,n.jsx)("p",{style:{fontWeight:"bold",margin:0},children:(null==e?void 0:e.last_name)+" "+(null==e?void 0:e.first_name)}),(0,n.jsx)("p",{style:{color:"rgb(100,100,100)",margin:0},children:null==e?void 0:e.email})]}),value:null==e?void 0:e.email})))},[t]);let v=async t=>{try{await (0,p.x4)({...t}).then(e=>{var t,n,r,i;localStorage.setItem("token",JSON.stringify(null==e?void 0:null===(t=e.responseData)||void 0===t?void 0:t.result)),l(null==e?void 0:null===(i=e.responseData)||void 0===i?void 0:null===(r=i.result)||void 0===r?void 0:null===(n=r.user)||void 0===n?void 0:n.email)})}catch(l){console.error("Login error:",l),r.ZP.error(e["common.login.error"])}};return(0,n.jsxs)(i.Z,{justify:"center",align:"top",className:"center-top-screen flex flex-col",children:[(0,n.jsxs)(o.default,{style:{width:"400px",padding:"20px",borderRadius:"10px",backgroundColor:"#fff",marginBottom:"20px"},onFinish:v,children:[(0,n.jsxs)("div",{style:{display:"flex",flexDirection:"column",alignItems:"center"},children:[(0,n.jsx)(s.Z,{src:"/images/Vietplants-logo.png",width:100,preview:!1}),(0,n.jsx)("p",{style:{marginTop:"20px",fontSize:"24px",fontWeight:"bold"},children:"Đăng nhập"})]}),(0,n.jsx)(u.Z,{name:"usr",children:(0,n.jsx)(a.Z,{options:m,size:"large",placeholder:"Chọn hoặc nhập t\xe0i khoản",filterOption:(e,l)=>null==l?void 0:l.value.toLowerCase().includes(e.toLowerCase())})}),(0,n.jsx)(u.Z,{name:"pwd",children:(0,n.jsx)(f.Z,{placeholder:"Mật khẩu",size:"large",type:"password"})}),(0,n.jsx)(d.ZP,{type:"primary",htmlType:"submit",style:{width:"100%"},size:"large",children:"Login"})]}),(0,n.jsx)(d.ZP,{disabled:!0,title:"Tạm thời chưa hỗ trợ lấy lại mật khẩu",type:"link",href:"/user/forgot-password",children:"Forgot password?"}),(0,n.jsxs)("p",{style:{marginTop:"30px",textAlign:"center",color:"rgb(100,100,100)"},children:[" ","\xa92025 Powered by VIIS"," "]})]})};l.default=m;let y=()=>(0,n.jsxs)(i.Z,{justify:"center",align:"top",className:"center-top-screen flex flex-col",children:[(0,n.jsxs)(o.default,{style:{width:"400px",padding:"20px",borderRadius:"10px",backgroundColor:"#fff",marginBottom:"20px"},children:[(0,n.jsx)("p",{style:{fontSize:"20px",fontWeight:"bold",marginBottom:"40px"},children:"Qu\xean mật khẩu"}),(0,n.jsx)(u.Z,{name:"username",children:(0,n.jsx)(c.Z,{placeholder:"Email người d\xf9ng",size:"large"})}),(0,n.jsx)(d.ZP,{type:"primary",htmlType:"submit",style:{width:"100%"},size:"large",children:"Lấy lại mật khẩu"})]}),(0,n.jsx)("a",{href:"/user/login",children:"Quay về trang đăng nhập"}),(0,n.jsxs)("p",{style:{marginTop:"30px",textAlign:"center",color:"rgb(100,100,100)"},children:[" ","\xa9 2025 Powered by VIIS"," "]})]})}},function(e){e.O(0,[467,899,478,979,888,774,179],function(){return e(e.s=8026)}),_N_E=e.O()}]);